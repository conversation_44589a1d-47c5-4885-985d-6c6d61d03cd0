# 智慧则南中央数据管理平台

## 项目简介

智慧则南中央数据管理平台是一个基于Vue 3的智慧城市数据可视化管理系统，主要用于展示和管理社区网格化数据、人口统计、经济结构、互动服务等信息。系统集成了3D地图展示、数据可视化图表、语音交互等功能，为城市管理提供全方位的数据支持。

## 技术栈

- **前端框架**: Vue 3 + Vite
- **UI组件库**: Element Plus
- **状态管理**: Vuex 4
- **路由管理**: Vue Router 4
- **数据可视化**: ECharts 5
- **3D地图**: Cesium
- **样式预处理**: Sass/SCSS
- **HTTP客户端**: Axios
- **屏幕适配**: vue3-scale-box, v-scale-screen
- **3D效果**: Three.js, Vanta.js
- **其他工具**: pinyin-pro (拼音转换)

## 主要功能

### 🏠 社区管理
- 社区基础数据展示
- 网格化管理
- 人口统计分析
- 户籍信息管理

### 📊 数据可视化
- 年龄结构分析图表
- 人口分布统计
- 经济结构展示
- 互动服务数据统计

### 🗺️ 3D地图展示
- 基于Cesium的3D地图
- 网格区域可视化
- 地理位置标注
- 交互式地图操作

### 🎤 语音交互
- 语音命令识别
- 智能导航控制
- 跨窗口消息通信

### 🌤️ 实时信息
- 天气信息显示
- 实时时间展示
- 系统状态监控

## 项目结构

```
src/
├── api/                    # API接口
├── assets/                 # 静态资源
├── components/             # 公共组件
│   ├── grid/              # 网格相关组件
│   ├── map/               # 地图组件
│   ├── ageData.vue        # 年龄数据组件
│   ├── allAge.vue         # 年龄统计组件
│   ├── allBranch.vue      # 分支统计组件
│   ├── allCommunity.vue   # 社区统计组件
│   ├── allHouseHolds.vue  # 户数统计组件
│   ├── allPeople.vue      # 人口统计组件
│   ├── bussinessCase.vue  # 经济案例组件
│   ├── interative.vue     # 互动数据组件
│   ├── peopleData.vue     # 人员数据组件
│   ├── resident.vue       # 居民信息组件
│   ├── safeData.vue       # 安全数据组件
│   ├── serviceCase.vue    # 服务案例组件
│   ├── speicalPeopleData.vue # 特殊人群数据组件
│   ├── streetData.vue     # 街道数据组件
│   └── table*.vue         # 表格组件
├── plugins/               # 插件配置
├── router/                # 路由配置
├── store/                 # 状态管理
├── views/                 # 页面组件
│   ├── login.vue          # 登录页面
│   ├── shequ.vue          # 社区主页
│   ├── wangge.vue         # 网格管理页
│   ├── a.vue              # 网格详情页A
│   ├── b.vue              # 网格详情页B (未使用)
│   ├── specificCommunity.vue # 特定社区页面
│   └── xiaoqu.vue         # 小区页面 (测试页面)
├── App.vue                # 根组件
├── main.js                # 入口文件
└── style.scss             # 全局样式
```

## 页面路由

| 路径 | 组件 | 说明 |
|------|------|------|
| `/` | - | 重定向到登录页 |
| `/login` | login.vue | 用户登录页面 |
| `/home` | shequ.vue | 社区主页 |
| `/grid` | wangge.vue | 网格管理页面 |
| `/communities` | a.vue | 网格详情页面 |
| `/specificCommunity` | specificCommunity.vue | 特定社区详情 |
| `/map` | cesium.vue | 3D地图页面 |
| `/mapA` | cesiumA.vue | 地图A视图 |
| `/mapB` | cesiumB.vue | 地图B视图 |

## 安装与运行

### 环境要求
- Node.js >= 14.0.0
- npm >= 6.0.0

### 安装依赖
```bash
npm install
```

### 开发环境运行
```bash
# 开发模式
npm run dev

# 生产模式预览
npm run dev:prod
```

### 构建项目
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## 配置说明

### 开发服务器配置
- 端口: 9000
- 自动打开浏览器
- 支持跨域访问

### 环境变量
项目支持多环境配置：
- `develop`: 开发环境
- `production`: 生产环境

### Cesium配置
项目使用Cesium进行3D地图展示，需要配置：
- Cesium Ion Access Token
- 3D瓦片数据源

## 主要特性

### 🎨 响应式设计
- 基于1920x1080分辨率设计
- 自适应屏幕缩放
- 支持全屏显示

### 📱 移动端适配
- 检测Android设备
- 针对移动端优化交互

### 🔐 权限控制
- 路由权限验证
- 登录状态管理

### 🌐 数据交互
- RESTful API接口
- 实时数据更新
- 跨窗口通信

## 开发指南

### 添加新组件
1. 在`src/components/`目录下创建组件文件
2. 在需要的页面中引入并注册
3. 添加相应的样式和逻辑

### 添加新页面
1. 在`src/views/`目录下创建页面组件
2. 在`src/router/index.js`中添加路由配置
3. 配置权限验证（如需要）

### API接口
所有API接口统一在`src/api/index.js`中管理，包括：
- 统计数据接口
- 天气信息接口
- 用户交互接口

## 浏览器支持

- Chrome >= 52
- Firefox >= 90
- Safari >= 15
- Edge >= 90

## 许可证

本项目为私有项目，版权所有。

## 联系方式

如有问题或建议，请联系开发团队。