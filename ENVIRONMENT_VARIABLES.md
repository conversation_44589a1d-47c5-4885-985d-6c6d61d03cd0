# 环境变量配置说明

## 🔍 问题解决

NODE_ENV警告问题已修复！

### ✅ 修复内容

**之前的问题**：
```bash
NODE_ENV=production is not supported in the .env file. Only NODE_ENV=development is supported to create a development build of your project.
```

**解决方案**：
- 从 `.env.production` 和 `.env.develop` 文件中移除了 `NODE_ENV` 设置
- Vite会根据运行的命令自动设置正确的 `NODE_ENV` 值

## 📁 环境文件配置

### .env.production
```bash
# NODE_ENV 由 Vite 自动设置，不需要在 .env 文件中定义
# NODE_ENV="production"
VITE_BASE_API="https://api.zn.nextv.show"
```

### .env.develop
```bash
# NODE_ENV 由 Vite 自动设置，不需要在 .env 文件中定义
# NODE_ENV="development"
VITE_BASE_API="https://api.zn.nextv.show"
```

## 🚀 运行命令与环境对应

| 命令 | 环境文件 | NODE_ENV | 说明 |
|------|----------|----------|------|
| `npm run dev` | `.env.develop` | `development` | 开发模式 |
| `npm run dev:prod` | `.env.production` | `development` | 生产配置的开发模式 |
| `npm run build` | `.env.production` | `production` | 生产构建 |
| `npm run preview` | `.env.production` | `production` | 预览构建结果 |

## 🔧 Vite环境变量规则

### 自动设置的变量
- `NODE_ENV`: 由Vite根据命令自动设置
- `MODE`: 通过 `--mode` 参数设置

### 自定义环境变量
- 必须以 `VITE_` 前缀开头
- 可以在 `.env` 文件中定义
- 在代码中通过 `import.meta.env.VITE_XXX` 访问

### 示例用法
```javascript
// 在代码中访问环境变量
const apiUrl = import.meta.env.VITE_BASE_API
const isDev = import.meta.env.DEV
const isProd = import.meta.env.PROD
const mode = import.meta.env.MODE
```

## 📊 验证结果

### 构建测试
- ✅ `npm run build` - 无警告，构建成功
- ✅ `npm run dev:prod` - 无警告，启动成功
- ✅ 环境变量正确加载

### 文件大小优化
构建后的文件已经过代码分割优化：
- 最大文件: `three-vendor-bfc6beee.js` (651.86 kB)
- 总构建时间: 13.35s
- 无NODE_ENV相关警告

## 🎯 最佳实践

1. **不要在.env文件中设置NODE_ENV**
   - Vite会自动管理这个变量
   - 手动设置会导致警告

2. **使用VITE_前缀**
   - 所有自定义环境变量必须以VITE_开头
   - 这样才能在客户端代码中访问

3. **环境文件命名**
   - `.env.development` - 开发环境
   - `.env.production` - 生产环境
   - `.env.local` - 本地覆盖（不提交到Git）

4. **安全考虑**
   - 不要在VITE_变量中存储敏感信息
   - 这些变量会暴露在客户端代码中

## 📞 相关链接

- [Vite环境变量文档](https://vitejs.dev/guide/env-and-mode.html)
- [Vue 3环境变量最佳实践](https://vuejs.org/guide/best-practices/production-deployment.html)
