# 文件路径:.github/workflows/deploy-to-production.yml

# 工作流的名称，将显示在GitHub的Actions标签页中
name: Deploy to Production

# 触发条件：当有代码推送到'main'分支时触发
on:
  push:
    branches:
      - main

# 定义工作流中的作业
jobs:
  # 作业ID，可自定义
  build-and-deploy:
    # 指定作业运行的Runner环境
    runs-on: ubuntu-latest

    # 作业中包含的步骤，将按顺序执行
    steps:
      # 步骤1：检出代码
      # 使用官方的actions/checkout@v4，将仓库代码下载到Runner中
      - name: Checkout Code
        uses: actions/checkout@v4

      # 步骤2：设置Node.js环境
      # 使用官方的actions/setup-node@v4，安装指定版本的Node.js
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18' # 指定Node.js版本，Vite推荐使用Node.js 18+
          cache: 'npm'       # 启用对npm依赖的缓存，加速后续构建

      # 步骤3：安装项目依赖
      # 'npm ci'命令用于CI环境，它会根据package-lock.json进行确定性安装
      - name: Install Dependencies
        run: npm ci

      # 步骤4：运行测试（如果存在 test 脚本则运行，否则跳过）
      - name: Run Tests (if present)
        run: |
          if npm run | grep -qE '^\s*test\s'; then
            npm test
          else
            echo "No test script defined, skipping."
          fi

      # 步骤5：构建生产应用
      # Vite项目使用npm run build命令构建生产版本
      - name: Build Vite Application
        run: npm run build

      # 步骤5.1：验证本地构建
      - name: Verify local build
        run: |
          echo "Checking local build..."
          ls -la dist/
          echo "index.html exists: $(test -f dist/index.html && echo 'YES' || echo 'NO')"
          echo "index.html size: $(stat -c%s dist/index.html 2>/dev/null || echo 'N/A') bytes"

      # 步骤6：在CI中构建并通过SCP上传产物到服务器
      - name: Prepare target directory on server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.ioc"
            echo "Preparing directory: $APP_DIR"
            mkdir -p "$APP_DIR"
            echo "Cleaning existing files..."
            rm -rf "$APP_DIR"/*
            echo "Directory prepared successfully"

      - name: Upload build artifacts via SCP
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "dist/"
          target: "/www/zn/zn.ioc/"
          strip_components: 0
          overwrite: true

      - name: Verify upload
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.ioc"
            DIST_DIR="$APP_DIR/dist"
            echo "Verifying upload to $DIST_DIR"
            echo "Base directory contents:"
            ls -la "$APP_DIR"
            echo "Dist directory contents:"
            ls -la "$DIST_DIR"
            echo "Checking for index.html in dist directory..."
            if [ -f "$DIST_DIR/index.html" ]; then
              echo "✓ index.html found in $DIST_DIR"
              echo "File size: $(stat -c%s "$DIST_DIR/index.html") bytes"
            else
              echo "✗ index.html NOT found in $DIST_DIR"
              echo "Available files in dist:"
              find "$DIST_DIR" -name "*.html" -o -name "*.js" -o -name "*.css" 2>/dev/null | head -10 || echo "No files found"
              exit 1
            fi

      - name: Finalize on server (permissions & reload nginx)
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.ioc"
            DIST_DIR="$APP_DIR/dist"

            # 设置正确的权限
            chown nginx:nginx -R "$APP_DIR"
            chmod 755 -R "$APP_DIR"

            # 验证关键文件是否存在
            if [ ! -f "$DIST_DIR/index.html" ]; then
              echo "ERROR: index.html not found in $DIST_DIR"
              exit 1
            fi

            # 检查并重载nginx
            if command -v nginx >/dev/null 2>&1; then
              nginx -t
              nginx -s reload
              echo "Nginx reloaded successfully"
            else
              echo "nginx not found, skip reload."
            fi

            # 输出部署信息
            echo "Deployment completed successfully"
            echo "Files in $APP_DIR:"
            ls -la "$APP_DIR" | head -10
            echo "Files in $DIST_DIR:"
            ls -la "$DIST_DIR" | head -10

      # 步骤7：通过SSH部署到生产服务器（已弃用的服务器端构建步骤）
      - name: "DEPRECATED: Old server-side build step (no-op)"
        if: ${{ false }}
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          # 在服务器上执行的脚本
          script: |
            # 以下是在生产服务器上执行的命令，请根据您的实际部署流程进行定制
            # 这是一个使用PM2管理Node.js应用的示例

            # 1. 进入项目目录
            cd /www/zn/zn.ioc

            # 2. 拉取最新的代码
            echo "Deprecated deploy step: skip git pull (using SCP-based deploy)."

            # 3. 安装生产环境依赖
            echo "skip npm install on server"

            # 4. 如果需要在服务器端构建，则执行构建命令
            echo "skip build on server"

            # 5. 重启应用
            echo "skip chown on deprecated step"
            echo "skip chmod on deprecated step"
            echo "skip nginx reload on deprecated step"
