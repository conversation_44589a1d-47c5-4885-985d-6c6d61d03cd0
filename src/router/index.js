import { createRouter, createWebHistory } from 'vue-router'
// import HelloWorld from '../components/HelloWorld.vue'

// 根据环境设置base路径
const base = import.meta.env.PROD ? '/' : '/'
const routerHistory = createWebHistory(base)

const router = createRouter({
  history: routerHistory,
  routes: [
    {
      path: '/',
      redirect: '/login',
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/login.vue'),
      meta: {
        auth: false,
      },
    },
    {
      path: '/home',
      name: 'Home',
      component: () => import('../views/shequ.vue'),
      meta: {
        auth: true,
      },
    },
    {
      path: '/grid',
      name: 'wangge',
      component: () => import('../views/wangge.vue'),
      meta: {
        auth: true,
      },
    },
    {
      path: '/communities',
      name: 'a',
      component: () => import('../views/a.vue'),
      meta: {
        auth: true,
      },
    },
    // {
    //   path: '/b',
    //   name: 'b',
    //   component: () => import('../views/b.vue'),
    // },
    // {
    //   path: '/xiaoqu',
    //   name: 'xiaoqu',
    //   component: () => import('../views/xiaoqu.vue'),
    // },
    {
      path: '/specificCommunity',
      name: 'specificCommunity',
      component: () => import('../views/specificCommunity.vue'),
      meta: {
        auth: true,
      },
    },
    {
      path: '/map',
      name: 'map',
      component: () => import('../components/map/cesium.vue'),
    },
    {
      path: '/mapA',
      name: 'mapA',
      component: () => import('../components/map/cesiumA.vue'),
    },
    {
      path: '/mapB',
      name: 'mapB',
      component: () => import('../components/map/cesiumB.vue'),
    },
  ],
})

// router.beforeEach((to, from, next) => {
//   let token = localStorage.getItem('token')
//   if (to.meta.auth) {
//     // 判断该路由是否需要登录权限
//     if (token) {
//       // 判断是否已经登录
//       next()
//     } else {
//       next({ path: '/login' }) //跳转到登录页
//     }
//   } else {
//     next()
//   }
// })


export default router
