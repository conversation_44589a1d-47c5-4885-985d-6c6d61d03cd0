<template>
  <div ref="age" id="age"></div>
  <div class="subtext">
    <span class="name">未成年人：</span><span class="n1">{{ props.subObj.teenager }}</span
    ><span class="per">({{ props.subObj.teenager_percent }})</span>
    &emsp;
    <span class="name">青壮年：</span><span class="n2">{{ props.subObj.middle_aged }}</span
    ><span class="per">({{ props.subObj.middle_aged_percent }})</span>
    &emsp;
    <span class="name">老年人：</span><span class="n3">{{ props.subObj.old_aged }}</span
    ><span class="per">({{ props.subObj.old_aged_percent }})</span>
  </div>

  <div class="pie">
    <div ref="person" id="person" @click="showTable1"></div>
    <div ref="special" id="special" @click="showTable2"></div>
    <div ref="important" id="important" @click="showTable3"></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted, getCurrentInstance, ref, onUnmounted } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
// 声明组件要触发的事件
const emits = defineEmits(['dialog'])
// 声明接收参数
const props = defineProps({
  dataPerson: Array,
  dataAge: Array,
  dataImportant: Array,
  dataSpecial: Array,
  subObj: Object,
})
// //  点击事件
// const handelClick = () => {
//   console.log('触发子组件点击')
//   // 触发父组件事件
//   emits('increase')
// };
const age = ref(null)
const person = ref(null)
const special = ref(null)
const important = ref(null)

onMounted(() => {
  setTimeout(() => {
    init1()
    init4()
    init5()
    init6()
  }, 500)
})
// 年龄构成图表
function init1() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.age;
  const myEcharts = echarts.init(age.value)
  var option
  var xData = [],
    yData = []
  var min = 50
  props.dataAge.map(function (a, b) {
    xData.push(a.name)
    if (a.value === 0) {
      yData.push(a.value + min)
    } else {
      yData.push(a.value)
    }
  })
  option = {
    title: {
      text: '年龄构成',
      // subtext: '{a|20}' + '{b|30}',
      left: 'center',
      textStyle: {
        color: '#fff',
      },
      top: -2,
    },
    backgroundColor: 'transparent',
    color: ['#3398DB'],
    // tooltip: {
    //   trigger: 'axis',
    //   axisPointer: {
    //     type: 'line',
    //     lineStyle: {
    //       opacity: 0
    //     }
    //   },
    //   formatter: function (prams) {
    //     return "合格率：" + prams[0].data + "%"
    //   }
    // },
    grid: {
      left: '5%',
      right: '5%',
      // bottom: '5%',
      top: '20%',
      // height: '85%',
      containLabel: true,
      // z: 22
    },
    xAxis: [
      {
        type: 'category',
        gridIndex: 0,
        data: xData,
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: 'white',
          },
        },
        axisLabel: {
          show: true,
          color: 'white',
          fontSize: 14,
        },
      },
    ],
    yAxis: {
      type: 'value',
      splitNumber: 4,
      interval: 1000,
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      min: 0,
      axisLine: {
        lineStyle: {
          color: 'white',
          width: 10,
        },
      },
      axisLabel: {
        color: 'white',
        formatter: '{value}',
      },
    },
    series: [
      {
        name: '人数',
        type: 'bar',
        barWidth: '30%',
        xAxisIndex: 0,
        yAxisIndex: 0,
        itemStyle: {
          barBorderRadius: [30, 30, 0, 0],
          color: '#2FC6DC',
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)',
        },
        data: yData,
        label: {
          show: true,
          position: 'top',
          color: 'white',
        },
        zlevel: 11,
      },
    ],
  }
  option && myEcharts.setOption(option)
  myEcharts.getZr().on('click', (params) => {
    let pointInPixel = [params.offsetX, params.offsetY]
    if (myEcharts.containPixel('grid', pointInPixel)) {
      let xIndex = myEcharts.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[0]
      // console.log(xIndex)
      switch (xIndex) {
        case 1:
          emits('dialogAge', 1)
          store.dispatch('changeAge', 1)
          break
        case 2:
          emits('dialogAge', 2)
          store.dispatch('changeAge', 2)
          break
        case 3:
          emits('dialogAge', 3)
          store.dispatch('changeAge', 3)
          break
        case 4:
          emits('dialogAge', 4)
          store.dispatch('changeAge', 4)
          break
        case 5:
          emits('dialogAge', 5)
          store.dispatch('changeAge', 5)
          break
        case 6:
          emits('dialogAge', 6)
          store.dispatch('changeAge', 6)
          break
        default:
          emits('dialogAge', 0)
          store.dispatch('changeAge', 0)
          break
      }
      store.dispatch('changeMask', true)
      store.dispatch('changeClosedAge', true)
      store.dispatch('changeClosedUpdate', true)
    }
  })
}
// 社区名人构成图表
function init4() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.person;
  const myEcharts = echarts.init(person.value)
  const colorList = [
    [
      { offset: 0, color: 'rgba(23, 77, 216, 1)' },
      { offset: 1, color: 'rgba(23, 77, 216, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(39, 89, 217, 1)' },
      { offset: 1, color: 'rgba(39, 89, 217, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(29, 100, 242, 1)' },
      { offset: 1, color: 'rgba(29, 100, 242, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(54, 116, 242, 1)' },
      { offset: 1, color: 'rgba(54, 116, 242, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(78, 136, 255, 1)' },
      { offset: 1, color: 'rgba(82, 139, 255, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(255, 205, 94, 1)' },
      { offset: 1, color: 'rgba(253, 203, 94, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(205, 94, 244, 1)' },
      { offset: 1, color: 'rgba(147, 42, 225, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(245, 146, 106, 1)' },
      { offset: 1, color: 'rgba(221, 75, 52, 1)' },
    ],
  ]
  const echartData = props.dataPerson.map((v, i) => ({
    name: v.name,
    value: v.num,
    itemStyle: {
      color: { type: 'linear', colorStops: colorList[i] },
    },
  }))
  var option = {
    title: {
      text: '社区名人',
      left: 'center',
      textStyle: {
        color: 'white',
      },
      top: 6,
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c} ({d}%)',
      backgroundColor: '#06111f',
      textStyle: {
        color: 'white',
      },
    },
    series: [
      {
        name: 'Area Mode',
        type: 'pie',
        radius: [30, 80],
        center: ['50%', '50%'],
        roseType: 'area',
        label: {
          position: 'inside',
          textStyle: {
            color: 'white',
            fontWeight: 'bold',
          },
          formatter: function (params) {
            return params.value + '\n' + params.name
          },
        },
        data: echartData,
      },
    ],
  }
  option && myEcharts.setOption(option)
}
// 特殊人群构成图表
function init5() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.special;
  const myEcharts = echarts.init(special.value)
  var option

  const echartData = props.dataSpecial.map((v, i) => ({
    name: v.name,
    value: v.num,
  }))

  option = {
    backgroundColor: 'transparent',
    title: {
      text: '特殊人群',
      textStyle: {
        color: '#fff',
      },
      left: 80,
      top: 7,
    },
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)',
      backgroundColor: '#00060b',
      textStyle: {
        color: 'white',
      },
      borderColor: '#00060b',
    },
    series: [
      {
        type: 'pie',
        selectedMode: 'single',
        radius: ['25%', '58%'],
        z: 1,
        color: ['#2759D9', '#4E88FF', '#1d64f2', '#78a4ff', '#174dd8'],
        label: {
          position: 'inner',
          formatter: '{c}\n{b}',
          textStyle: {
            color: '#fff',
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
        labelLine: {
          show: false,
        },
        data: echartData,
      },
      {
        type: 'pie',
        radius: ['0', '30%'],
        z: 0,
        itemStyle: {
          color: 'rgba(0,0,0,.3)',
          // emphasis: {
          //   color: '#ADADAD'
          // }
        },
        data: [0],
      },
    ],
  }

  option && myEcharts.setOption(option)
}
// 重点监控人群构成图表 ====》  改为军人构成
function init6() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.important;
  const myEcharts = echarts.init(important.value)
  const data1 = props.dataImportant.map((v) => {
    return {
      value: v.value,
      name: v.name,
    }
  })
  // const data2 = props.dataImportant.map(v => {
  //   return {
  //     value: v.num,
  //     name: v.name
  //   }
  // })
  var option = {
    title: {
      text: '军人构成',
      textStyle: {
        color: '#fff',
      },
      left: 60,
      top: 7,
    },
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.9)',
      formatter: function (params) {
        return params.marker + '<span style="color:white">' + params.data['name'] + '\n' + params.data['value'] + '</span>'
      },
    },
    series: [
      {
        name: '',
        type: 'pie',
        hoverAnimation: false,
        legendHoverLink: false,
        cursor: 'default',
        right: 20,
        radius: ['32%', '50%'],
        center: ['50%', '50%'],
        color: ['rgba(85, 149, 255,0.2)', 'rgba(15, 105, 241, 0.2)', 'rgba(48, 198, 220, 0.2)', 'rgba(78, 136, 255, 0.2)', 'rgba(15, 163, 241, 0.2)'],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        zlevel: 1,
        itemStyle: {
          borderColor: '#0a1a2a',
          ellipsis: {
            borderColor: '#0a1a2a',
          },
        },
        tooltip: {
          show: false,
        },
        data: data1,
      },
      {
        name: '',
        type: 'pie',
        zlevel: 2,
        right: 20,
        cursor: 'default',
        hoverAnimation: false,
        legendHoverLink: false,
        radius: ['37%', '50%'],
        center: ['50%', '50%'],
        color: ['rgba(85, 149, 255, 0.5)', 'rgba(15, 105, 241, 0.5)', 'rgba(48, 198, 220, 0.5)', 'rgba(78, 136, 255, 0.5)', 'rgba(15, 163, 241, 0.5)'],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        itemStyle: {
          borderColor: '#0a1a2a',
          ellipsis: {
            borderColor: '#0a1a2a',
          },
        },
        tooltip: {
          show: false,
        },
        data: data1,
      },
      {
        name: 'title',
        type: 'pie',
        zlevel: 3,
        right: 20,
        hoverAnimation: false,
        legendHoverLink: false,
        radius: ['43%', '58%'],
        center: ['50%', '50%'],
        color: ['rgba(85, 149, 255, 1)', 'rgba(15, 105, 241, 1)', 'rgba(48, 198, 220, 1)', 'rgba(78, 136, 255, 1)', 'rgba(15, 163, 241, 1)'],
        label: {
          show: true,
          formatter: (params) => {
            return `
              ${params.data.value}
              ${params.name}
              `
          },
          // padding: [0, -40],
          textStyle: {
            // lineHeight: 20,
            color: 'white',
            fontWeight: 'bold',
          },
          position: 'inside',
        },
        data: data1,
      },
    ],
  }
  option && myEcharts.setOption(option)
}
// 社区名人表格弹窗
function showTable1() {
  emits('dialog', 1)
  store.dispatch('changeNo', 4)
  store.dispatch('changeMask', true)
  store.dispatch('changeClosed1', true)
}
// 特殊人群表格弹窗
function showTable2() {
  emits('dialog', 2)
  store.dispatch('changeNo', 2)
  store.dispatch('changeMask', true)
  store.dispatch('changeClosed1', true)
}
// 重点监控人群表格弹窗
function showTable3() {
  emits('dialog', 3)
  store.dispatch('changeNo', 3)
  store.dispatch('changeMask', true)
  store.dispatch('changeClosed1', true)
}
onUnmounted(() => {})
</script>

<style lang="scss" scoped>
#age {
  width: 500px;
  height: 300px;
  margin: 20px auto;
}

.subtext {
  text-align: center;
  height: 20px;
  width: 100%;
  position: relative;
  top: -290px;

  .name {
    font-size: 12px;
    color: #edf2fa;
  }

  .n1 {
    color: #f1728c;
    font-weight: bold;
    font-size: 16px;
  }

  .n2 {
    color: #f4983c;
    font-weight: bold;
    font-size: 16px;
  }

  .n3 {
    color: #0fa3f1;
    font-weight: bold;
    font-size: 16px;
  }

  .per {
    color: white;
    font-size: 14px;
  }
}

.pie {
  width: 100%;
  display: flex;
  position: relative;
  left: 4%;
  top: -14%;

  #person {
    width: 240px;
    height: 240px;
    position: absolute;
    left: -11%;
  }

  #special {
    width: 240px;
    height: 240px;
    position: absolute;
    left: 22%;
  }

  #important {
    width: 240px;
    height: 240px;
    position: absolute;
    top: -2%;
    right: -2%;
  }
}
</style>
