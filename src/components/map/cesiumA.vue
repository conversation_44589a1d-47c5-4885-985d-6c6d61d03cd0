
<template>
  <div id="cesiumContainer"></div>
</template>
<script setup>
// 望凤街网格
import * as Cesium from 'cesium';
import { onMounted, onUnmounted, ref, reactive } from 'vue';
import { useRouter } from 'vue-router'
import { useStore } from 'vuex' // 引入useStore 方法
const store = useStore()  // 该方法用于返回store 实例
let handler = ref(null)
let viewer = ref(null)
let data = reactive({
  data: [
    {
      lng:
        105.81203,
      lat: 32.444008,
      infoId: 2026,
      text: '阳光水岸小区',
    },
    {
      lng: 105.812476,
      lat: 32.444794,
      infoId: 2025,
      text: '刑警队住宿楼',
    },
    {
      lng: 105.811654,
      lat:
        32.445405,
      infoId: 2013,
      text: '工行小区',
    },
    // {
    //   lng:
    //     105.812246,
    //   lat:
    //     32.445872,
    //   infoId: 4,
    //   text: '工行五六号园',
    // },
    {
      lng:
        105.812814,
      lat:
        32.446311,
      infoId: 2027,
      text: '樱花小区',
    },
    {
      lng:
        105.810693,
      lat:
        32.445391,
      infoId: 2021,
      text: '蔷薇小区',
    },
    {
      lng:
        105.810715,
      lat:
        32.445816,
      infoId: 2016,
      text: '皇佳公寓',
    },
    {
      lng:
        105.810417,
      lat:
        32.446181,
      infoId: 2015,
      text: '和顺嘉苑',
    },
    {
      lng:
        105.811796,
      lat:
        32.447072,
      infoId: 2029,
      text: '则天二小区',
    },
    {
      lng:
        105.812215,
      lat:
        32.446954,
      infoId: 2030,
      text: '则天一小区',
    },
    {
      lng:
        105.811091,
      lat:
        32.446584,
      infoId: 2024,
      text: '信用社住宿楼',
    },
    {
      lng:
        105.812182,
      lat:
        32.446527,
      infoId: 2017,
      text: '金矿新区',
    },
    {
      lng:
        105.811292,
      lat:
        32.445192,
      infoId: 2022,
      text: '嘉利水岸花园',
    },
    {
      lng:
        105.812737,
      lat:
        32.447696,
      infoId: 2023,
      text: '兴和万科花园',
    },
    {
      lng:
        105.813013,
      lat:
        32.448363,
      infoId: 2018,
      text: '金龙小区',
    },
    {
      lng:
        105.813306,
      lat:
        32.44815,
      infoId: 2019,
      text: '林业站小区',
    },
    {
      lng:
        105.81304,
      lat:
        32.447036,
      infoId: 2028,
      text: '雍江皇庭小区',
    },
    {
      lng:
        105.813357,
      lat:
        32.447368,
      infoId: 2032,
      text: '自来水公司住宿楼',
    },
    {
      lng:
        105.813983,
      lat:
        32.447098,
      infoId: 2031,
      text: '装备处住宿楼',
    },
    // {
    //   lng:
    //     105.813628,
    //   lat:
    //     32.447589,
    //   infoId: 20,
    //   text: '酒研所小区',
    // },
    // {
    //   lng:
    //     105.814153,
    //   lat:
    //     32.447898,
    //   infoId: 21,
    //   text: '龙江电力',
    // },
    {
      lng:
        105.814637,
      lat:
        32.447582,
      infoId: 2012,
      text: '工商所住宿楼',
    },
    {
      lng:
        105.813661,
      lat:
        32.446842,
      infoId: 2020,
      text: '派出所住宿楼',
    },
    {
      lng:
        105.81339,
      lat:
        32.446651,
      infoId: 2014,
      text: '管委会住宿楼',
    },
    {
      lng:
        105.81213,
      lat:
        32.447855,
      infoId: 2010,
      text: '陈怀宽住宿楼',
    },
    {
      lng:
        105.812479,
      lat:
        32.448122,
      infoId: 2011,
      text: '二建公司住宿楼',
    },
  ],
  list: [
    {
      value1: 2,
      value2: '网格数(个)'
    },
    {
      value1: 34,
      value2: '小区(个)'
    },
    {
      value1: 2468,
      value2: '户数(户)'
    },
    {
      value1: 5623,
      value2: '人数(人)'
    }
  ]
})

onMounted(() => {
  // const viewer = new Cesium.Viewer('cesiumContainer');
  // let custom = new Cesium.ArcGisMapServerImageryProvider({
  //   url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer'
  // })
  Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.oeQSme1YSkYiiXbr26yD0qSVT1yTIAwNLjDOkcCGg7M'
  viewer.value = new Cesium.Viewer('cesiumContainer', {
    // 稳定版
    geocoder: false,                //是否显示地名查找控件
    sceneModePicker: false,         //是否显示投影方式控件
    navigationHelpButton: false,    //是否显示帮助信息控件
    baseLayerPicker: true,         //是否显示图层选择控件
    homeButton: false,              //是否显示Home按钮
    fullscreenButton: false,        //是否显示全屏按钮    
    timeline: false,                 //时间轴控件 
    animation: false,                //动画控件 
    shouldAnimate: true,
    infoBox: false,//初始化不弹出弹出框
    selectionIndicator: false,//初始化不选中
    baseLayerPicker: false,
    // imageryProvider: custom,
    // 使用兼容的地形提供者
    terrainProvider: new Cesium.EllipsoidTerrainProvider(),
  })
  viewer.value.cesiumWidget.creditContainer.style.display = "none";//去cesium logo水印 或 css
  viewer.value.camera.setView({
    // destination: Cesium.Cartesian3.fromDegrees(105.818445, 32.448487, 3000.0),
    destination: Cesium.Cartesian3.fromDegrees(105.81466431496081, 32.**************, 600.0),
    orientation: {
      heading: Cesium.Math.toRadians(340.72605932003046),
      pitch: Cesium.Math.toRadians(-15.***************),
      roll: 0.006995150196427984
    }
  });
  // 3839  

  // 安全加载3D瓦片集
  try {
    // 检查 Cesium3DTileset 是否可用
    if (!Cesium.Cesium3DTileset) {
      console.warn('Cesium.Cesium3DTileset is not available')
    } else {
      const tile = new Cesium.Cesium3DTileset({
        url: 'https://cdn.zn.nextv.show/zn/tileset.json',
      })

      // 检查瓦片集对象是否有效
      if (!tile) {
        console.warn('Failed to create tileset - constructor returned null/undefined')
      } else {
        // 检查 readyPromise 是否存在
        if (tile.readyPromise && typeof tile.readyPromise.then === 'function') {
          // 监听瓦片集加载事件
          tile.readyPromise.then(() => {
            console.log('3D Tileset loaded successfully')
          }).catch((error) => {
            console.warn('3D Tileset failed to load:', error)
            // 移除失败的瓦片集
            if (viewer.value && viewer.value.scene) {
              try {
                viewer.value.scene.primitives.remove(tile)
              } catch (removeError) {
                console.warn('Error removing failed tileset:', removeError)
              }
            }
          })
        } else {
          console.warn('Tileset readyPromise is not available, adding to scene directly')
        }

        // 添加到场景
        if (viewer.value && viewer.value.scene) {
          viewer.value.scene.primitives.add(tile)
        }
      }
    }
  } catch (error) {
    console.warn('Failed to create 3D Tileset:', error)
  }
  viewer.value.scene.globe.depthTestAgainstTerrain = true;
  // viewer.value.zoomTo(
  //   tile,
  //   // new Cesium.HeadingPitchRange(
  //   //   0.0,
  //   //   -0.5,
  //   //   tile.boundingSphere.radius * 1.0
  //   // )
  // );
  // 绘制蒙版  区分区域
  // viewer.value.entities.add({
  //   polygon: {
  //     hierarchy: Cesium.Cartesian3.fromDegreesArray([105.809825, 32.446426, 105.813212, 32.449186, 105.815133, 32.44765, 105.813891, 32.446415, 105.811862, 32.442634, 105.811501, 32.442315,]),
  //     material: Cesium.Color.fromCssColorString('#f63d3f').withAlpha(0.5),
  //     classificationType: Cesium.ClassificationType.TERRAIN, // classificationType: ClassificationType.TERRAIN,
  //     // HeightReference: Cesium.HeightReference.NONE,
  //     // clampToGround: true,
  //   },
  // })
  // 设置相机最大可到高度
  viewer.value.scene.screenSpaceCameraController.maximumZoomDistance = 2000;
  // 标记点位  可点击
  let x = 1;
  let flog = true;
  data.data.map((list, index) => {
    viewer.value.entities.add({
      id: `area${index}`,
      infoId: list.infoId,
      position: new Cesium.Cartesian3.fromDegrees(list.lng, list.lat, 500.0),

      billboard: {
        image: '/zuobiao.png',
        scale: 0.5,
        // rotation: Cesium.Math.toRadians(45),
        pixelOffset: new Cesium.Cartesian2(0, 20)
      },
      label: {
        //文字标签
        text: list.text.split('').join('\n'),
        font: '500 30px Helvetica',// 15pt monospace
        scale: 0.5,
        style: Cesium.LabelStyle.FILL,
        fillColor: Cesium.Color.WHITE,
        showBackground: true,
        backgroundColor: new Cesium.Color(228, 76, 76, 1.0),
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BASELINE,
      },
    })
  })
  handlePointClick();//添加点的点击事件
  let handler = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas);
  handler.setInputAction(function (event) {
    // getCurrentExtent()
    let ray = viewer.value.camera.getPickRay(event.position);
    let cartesian = viewer.value.scene.globe.pick(ray, viewer.value.scene);
    let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
    let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
    let alt = cartographic.height; // 高度
    let coordinate = {
      longitude: Number(lng.toFixed(6)),
      latitude: Number(lat.toFixed(6)),
      altitude: Number(alt.toFixed(2))
    };
    console.log(coordinate);
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  // 电子围墙
  let dataZ = [[105.818, 32.446155, 4000], [105.827, 32.456, 4000], [105.830, 32.456, 4000], [105.8234, 32.449, 4000]]
  dataZ.forEach(bound => {
    var entity = createAoiByCesium({
      color: new Cesium.Color(42 / 255, 165 / 255, 247 / 255, 0.5),
      show: true,
      positions: [105.809825, 32.446426, 10, 105.813169, 32.44911, 10, 105.815061,
        32.447432, 10, 105.813891, 32.446415, 10, 105.811862, 32.442634, 10, 105.811501, 32.442315, 10, 105.809825, 32.446426, 10], // 格式需要为一维数组[114.112, 22.223, 40, 114.113, 22.224, 40]
      wallHeight: 480,
      hasHeight: true,
    })
    entity.tag = 'areaDsLines'
    if (!window.shiningWalls) {
      window.shiningWalls = []
    }
    window.shiningWalls.push(entity)
  })
  // // 第二个电子围墙
  // let dataA = [[105.818, 32.446155, 4000], [105.827, 32.456, 4000], [105.830, 32.456, 4000], [105.8234, 32.449, 4000]]
  // dataA.forEach(bound => {
  //   var entity = createAoiByCesium({
  //     color: new Cesium.Color(66 / 255, 66 / 255, 200 / 255, 0.5),
  //     show: true,
  //     positions: [
  //       105.813239, 32.449161, 10, 105.815061, 32.447548, 10,
  //       105.81871, 32.450701, 10, 105.817079, 32.452158, 10, 105.813239, 32.449161, 10], // 格式需要为一维数组[114.112, 22.223, 40, 114.113, 22.224, 40]
  //     wallHeight: 550,
  //     hasHeight: true,
  //   })
  //   entity.tag = 'areaDsLines'
  //   if (!window.shiningWalls) {
  //     window.shiningWalls = []
  //   }
  //   window.shiningWalls.push(entity)
  // })

})
function handlePointClick() {
  handler.value = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas);
  handler.value.setInputAction(function (click) {
    let pick = viewer.value.scene.pick(click.position)
    if (pick && pick.id) {
      viewer.value.entities.values.map((item) => {
        if (pick.id.id && (item.id == pick.id.id)) {
          // that.viewer._selectedEntity = [];//去除左击之后出现选中的绿框
          if (item.infoId) {
            // 点击点之后的操作
            window.parent.postMessage({ 'type': 'a', 'value': item.infoId, 'name': item.label.text._value }, "*");
          }
        }
      })
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
  viewer.value.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);//去除双击放大地图效果
};
function createAoiByCesium(option) {
  var color = option.color ? option.color : Cesium.Color.RED
  var maxHeight = option.maxHeight ? option.maxHeight : 10
  var minHeight = option.minHeight ? option.minHeight : 1
  var maxHeightArray = []
  var minHeightArray = []

  var c = document.createElement('canvas')
  c.width = 50
  c.height = 50
  var ctx = c.getContext('2d')

  var grd = ctx.createLinearGradient(0, 20, 0, 0)
  grd.addColorStop(0, 'rgba(' + color.red * 255 + ',' + color.green * 255 + ',' + color.blue * 255 + ',1)')
  grd.addColorStop(1, 'rgba(' + color.red * 255 + ',' + color.green * 255 + ',' + color.blue * 255 + ',0)')

  ctx.fillStyle = grd
  ctx.fillRect(0, 0, 50, 50)

  var config = null
  if (option.hasHeight) {
    var positions = []
    option.positions.forEach((x, index) => {
      if (index % 3 == 2) {
        minHeightArray.push(x)
        maxHeightArray.push(x + (isNaN(option.wallHeight) ? 1 : option.wallHeight))
      } else {
        positions.push(x)
      }
    })
    config = {
      wall: {
        show: option.show == false ? false : true,
        positions: Cesium.Cartesian3.fromDegreesArray(positions),
        material: new Cesium.ImageMaterialProperty({
          image: c,
          transparent: true,
        }),
        zIndex: 1000
      },
    }
  } else {
    for (var i = 0; i < option.positions.length / 2; i++) {
      minHeightArray.push(minHeight)
      maxHeightArray.push(maxHeight)
    }
    config = {
      wall: {
        show: option.show == false ? false : true,
        positions: Cesium.Cartesian3.fromDegreesArray(option.positions),
        material: new Cesium.ImageMaterialProperty({
          image: c,
          transparent: true,
        }),
        zIndex: 1000
      },
    }
  }
  config.wall.maximumHeights = maxHeightArray
  config.wall.minimumHeights = minHeightArray
  var entity1 = viewer.value.entities.add(config)

  entity1.wall.material.color = new Cesium.CallbackProperty(function (time, x) {
    var alp = 0.5 * Math.abs(Math.sin(new Date().getTime() / 500)) + 0.1
    return color.withAlpha(alp)
  }, false)
  return entity1
};

// 获取当前相机的俯角等信息  左击即可
// function getCurrentExtent() {
//   // 范围对象
//   var extent = {};
//   // 得到当前三维场景
//   var scene = viewer.value.scene;
//   // 得到当前三维场景的椭球体
//   var ellipsoid = scene.globe.ellipsoid;
//   var canvas = scene.canvas;
//   // canvas左上角
//   var car3_lt = viewer.value.camera.pickEllipsoid(new Cesium.Cartesian2(0, 0),
//     ellipsoid);
//   // canvas右下角
//   var car3_rb = viewer.value.camera.pickEllipsoid(new Cesium.Cartesian2(
//     canvas.width, canvas.height), ellipsoid);
//   // 当canvas左上角和右下角全部在椭球体上
//   if (car3_lt && car3_rb) {
//     var carto_lt = ellipsoid.cartesianToCartographic(car3_lt);
//     var carto_rb = ellipsoid.cartesianToCartographic(car3_rb);
//     extent.xmin = Cesium.Math.toDegrees(carto_lt.longitude);
//     extent.ymax = Cesium.Math.toDegrees(carto_lt.latitude);
//     extent.xmax = Cesium.Math.toDegrees(carto_rb.longitude);
//     extent.ymin = Cesium.Math.toDegrees(carto_rb.latitude);
//   }
//   // 当canvas左上角不在但右下角在椭球体上
//   else if (!car3_lt && car3_rb) {
//     var car3_lt2 = null;
//     var yIndex = 0;
//     do {
//       // 这里每次10像素递加，一是10像素相差不大，二是为了提高程序运行效率
//       yIndex <= canvas.height ? yIndex += 10 : canvas.height;
//       car3_lt2 = viewer.value.camera.pickEllipsoid(new Cesium.Cartesian2(0,
//         yIndex), ellipsoid);
//     } while (!car3_lt2);
//     var carto_lt2 = ellipsoid.cartesianToCartographic(car3_lt2);
//     var carto_rb2 = ellipsoid.cartesianToCartographic(car3_rb);
//     extent.xmax = Cesium.Math.toDegrees(carto_lt2.longitude);
//     extent.ymax = Cesium.Math.toDegrees(carto_lt2.latitude);
//     extent.xmin = Cesium.Math.toDegrees(carto_rb2.longitude);
//     extent.ymin = Cesium.Math.toDegrees(carto_rb2.latitude);
//   }
//   // 获取高度
//   extent.height = Math.ceil(viewer.value.camera.positionCartographic.height);
//   extent.lon = Cesium.Math
//     .toDegrees(viewer.value.camera.positionCartographic.longitude);
//   extent.lat = Cesium.Math
//     .toDegrees(viewer.value.camera.positionCartographic.latitude);
//   extent.heading = Cesium.Math.toDegrees(viewer.value.camera.heading);
//   extent.pitch = Cesium.Math.toDegrees(viewer.value.camera.pitch);
//   extent.roll = Cesium.Math.toDegrees(viewer.value.camera.roll);
//   //console.log("lon："+extent.lon+"--lat："+extent.lat+"--height："+extent.height+"--heading：" + extent.heading + "--pitch：" + extent.pitch
//   //+ "--roll：" + extent.roll);
//   //console.log('{"lon":"'+extent.lon+'","lat":"'+extent.lat+'","height":"'+extent.height+'","heading":"'+extent.heading+'","pitch":"'+extent.pitch+'","roll":"'+extent.roll+'"}');
//   console.log("{'lon':" + extent.lon + ",'lat':" + extent.lat + ",'height':" + extent.height + ",'heading':" + extent.heading + ",'pitch':" + extent.pitch + ",'roll':" + extent.roll + "}");

//   return extent;
// }




onUnmounted(() => {
  // 清理事件监听器
  if (handler.value) {
    handler.value.destroy()
    handler.value = null
  }

  // 销毁viewer
  if (viewer.value) {
    viewer.value.destroy()
    viewer.value = null
  }
})
</script>
<style scoped lang="scss">
#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
</style>
