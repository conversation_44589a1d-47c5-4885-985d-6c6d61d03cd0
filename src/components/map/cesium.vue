<template>
  <div id="cesiumContainer"></div>
</template>
<script setup>
import * as Cesium from 'cesium'
import { onMounted, onUnmounted, ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex' // 引入useStore 方法
import img1 from '/sanjiao01.png'
import img2 from '/sanjiao02.png'
const store = useStore() // 该方法用于返回store 实例
const router = useRouter()
let handler = ref(null)
let viewer = ref(null)
let postRenderHandler = ref(null) // 存储postRender事件处理器
let data = reactive({
  data: [
    {
      lng: 105.812686,
      lat: 32.447056,
      infoId: 1,
      text: 'A街区网格',
      color: '#a460f5',
    },
    {
      lng: 105.816116,
      lat: 32.4502,
      infoId: 2,
      text: 'B街区网格',
      color: '#f66501',
    },
  ],
  list: [
    {
      value1: 2,
      value2: '网格数(个)',
    },
    {
      value1: 34,
      value2: '小区(个)',
    },
    {
      value1: 2468,
      value2: '户数(户)',
    },
    {
      value1: 5623,
      value2: '人数(人)',
    },
  ],
  dataArr: [
    { name: '女', num: 2727 },
    { name: '男', num: 2856 },
  ],
})

onMounted(() => {
  // const viewer = new Cesium.Viewer('cesiumContainer');
  // let custom = new Cesium.ArcGisMapServerImageryProvider({
  //   url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer'
  // })

  Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.oeQSme1YSkYiiXbr26yD0qSVT1yTIAwNLjDOkcCGg7M'
  viewer.value = new Cesium.Viewer('cesiumContainer', {
    // 稳定版
    geocoder: false, //是否显示地名查找控件
    sceneModePicker: false, //是否显示投影方式控件
    navigationHelpButton: false, //是否显示帮助信息控件
    baseLayerPicker: true, //是否显示图层选择控件
    homeButton: false, //是否显示Home按钮
    fullscreenButton: false, //是否显示全屏按钮
    timeline: false, //时间轴控件
    animation: false, //动画控件
    shouldAnimate: true,
    infoBox: false, //初始化不弹出弹出框
    selectionIndicator: false, //初始化不选中
    baseLayerPicker: false,
    // 使用兼容的地形提供者
    terrainProvider: new Cesium.EllipsoidTerrainProvider(),
    // sceneOptions: {
    //   requestRenderMode: true,
    //   heightReference: 5000
    // }
  })

  viewer.value.cesiumWidget.creditContainer.style.display = 'none' //去cesium logo水印 或 css
  // viewer.value.camera.setView({
  //   destination: Cesium.Cartesian3.fromDegrees(105.81239, 32.44654, 3000.0),
  //   // destination: Cesium.Cartesian3.fromDegrees(113.318977, 23.114155, 1500.0),
  //   // orientation: {
  //   //   // heading: Cesium.Math.toRadians(),
  //   //   // pitch: Cesium.Math.toRadians(-45),
  //   // }
  // });
  // 3839

  // 安全加载3D瓦片集
  try {
    const tile = new Cesium.Cesium3DTileset({
      url: 'https://cdn.zn.nextv.show/zn/tileset.json',
    })

    // 监听瓦片集加载事件
    tile.readyPromise.then(() => {
      console.log('3D Tileset loaded successfully')
      // 只有在成功加载后才进行缩放
      if (viewer.value) {
        viewer.value.zoomTo(tile)
      }
    }).catch((error) => {
      console.warn('3D Tileset failed to load:', error)
      // 移除失败的瓦片集
      if (viewer.value && viewer.value.scene) {
        viewer.value.scene.primitives.remove(tile)
      }
    })

    viewer.value.scene.primitives.add(tile)
    viewer.value.scene.globe.depthTestAgainstTerrain = true
  } catch (error) {
    console.warn('Failed to create 3D Tileset:', error)
  }

  // 绘制蒙版  区分区域
  // viewer.value.entities.add({
  //   polygon: {
  //     hierarchy: Cesium.Cartesian3.fromDegreesArray([105.809825, 32.446426, 105.813212, 32.449186, 105.815133, 32.44765, 105.813891, 32.446415, 105.811862, 32.442634, 105.811501, 32.442315,]),
  //     material: Cesium.Color.fromCssColorString('#f63d3f').withAlpha(0.5),
  //     classificationType: Cesium.ClassificationType.TERRAIN, // classificationType: ClassificationType.TERRAIN,
  //     // HeightReference: Cesium.HeightReference.NONE,
  //     // clampToGround: true,
  //   },
  // })

  // 标记点位  可点击
  let x = 1
  let flog = true
  data.data.map((list, index) => {
    let div = document.createElement('div')
    div.setAttribute('id', index)
    div.style.position = 'absolute'
    div.style.width = '240px'
    div.style.height = '240px'
    div.style.textAlign = 'center'
    div.style.fontSize = '30px'
    div.style.color = 'white'
    div.style.fontWeight = 'bold'
    div.style.lineHeight = '50px'
    div.style.cursor = 'pointer'
    let HTMLTable =
      index == 0
        ? `
		<div style="background:linear-gradient(to top,#2da8f5,#60d8d3);border-radius:10px">望凤街网格</div>
    <img src="${img1}" alt="" id="img1">
    `
        : `
		<div style="background:linear-gradient(to top, #7def47, #36d057);border-radius:10px">中兴街网格</div>
    <img src="${img2}" alt="" id="img2">
    `
    div.innerHTML = HTMLTable
    viewer.value.cesiumWidget.container.appendChild(div)
    let gisPosition = Cesium.Cartesian3.fromDegrees(list.lng, list.lat, 500.0)

    // 创建postRender事件处理器
    postRenderHandler.value = () => {
      // 检查viewer和相关对象是否仍然有效
      if (!viewer.value || !viewer.value.scene || !viewer.value.scene.canvas || !div) {
        return
      }

      try {
        const canvasHeight = viewer.value.scene.canvas.height
        // 使用兼容的坐标转换方法
        const windowPosition = Cesium.SceneTransforms.wgs84ToWindowCoordinates ?
          Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.value.scene, gisPosition) :
          new Cesium.Cartesian2(canvasHeight / 2, canvasHeight / 2) // 默认位置

        if (windowPosition && div.parentNode) {
          div.style.bottom = canvasHeight - windowPosition.y + 'px'
          const elWidth = div.offsetWidth
          div.style.left = windowPosition.x - elWidth / 2 + 'px'
        }
      } catch (error) {
        console.warn('Cesium coordinate transformation failed:', error)
        // 使用默认位置
        if (div && div.parentNode) {
          div.style.bottom = '50%'
          div.style.left = '50%'
          div.style.transform = 'translateX(-50%)'
        }
      }
    }

    // 添加事件监听器
    viewer.value.scene.postRender.addEventListener(postRenderHandler.value)
    div.addEventListener('click', function () {
      clickFun(index)
    })
  })
  document.getElementById('img1').animate(
    [
      {
        transform: 'translateY(20px)',
      },
    ],
    {
      duration: 1000,
      easing: 'linear',
      iterations: 'Infinity',
      // fill: 'forwards',
      delay: 0,
      // direction: 'alternate'
    }
  )
  document.getElementById('img2').animate(
    [
      {
        transform: 'translateY(20px)',
      },
    ],
    {
      duration: 1000,
      easing: 'linear',
      iterations: 'Infinity',
      // fill: 'forwards',
      delay: 0,
      // direction: 'alternate'
    }
  )
  handler.value = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas)
  handler.value.setInputAction(function (movement) {
    //具体事件的实现
    var ellipsoid = viewer.value.scene.globe.ellipsoid
    //捕获椭球体，将笛卡尔二维平面坐标转为椭球体的笛卡尔三维坐标，返回球体表面的点
    var cartesian = viewer.value.camera.pickEllipsoid(movement.endPosition, ellipsoid)
    if (cartesian) {
      //将笛卡尔三维坐标转为地图坐标（弧度）
      var cartographic = viewer.value.scene.globe.ellipsoid.cartesianToCartographic(cartesian)
      //将地图坐标（弧度）转为十进制的度数
      var lat_String = Cesium.Math.toDegrees(cartographic.latitude).toFixed(4)
      var log_String = Cesium.Math.toDegrees(cartographic.longitude).toFixed(4)
      var alti_String = (viewer.value.camera.positionCartographic.height / 1000).toFixed(2)
      if (alti_String >= 1.5) {
        Array.from(document.getElementsByTagName('div')).map((v) => {
          if (v.id == '0' || v.id == '1') {
            v.style.display = 'none'
          }
        })
      } else {
        Array.from(document.getElementsByTagName('div')).map((v) => {
          if (v.id == '0' || v.id == '1') {
            v.style.display = 'block'
          }
        })
      }
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)

  let handler1 = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas)
  handler1.setInputAction(function (event) {
    getCurrentExtent()
    let ray = viewer.value.camera.getPickRay(event.position)
    let cartesian = viewer.value.scene.globe.pick(ray, viewer.value.scene)
    let cartographic = Cesium.Cartographic.fromCartesian(cartesian)
    let lng = Cesium.Math.toDegrees(cartographic.longitude) // 经度
    let lat = Cesium.Math.toDegrees(cartographic.latitude) // 纬度
    let alt = cartographic.height // 高度
    let coordinate = {
      longitude: Number(lng.toFixed(6)),
      latitude: Number(lat.toFixed(6)),
      altitude: Number(alt.toFixed(2)),
    }
    console.log(coordinate)
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

  // 电子围墙  42, 165, 247
  let dataZ = [
    [105.818, 32.446155, 4000],
    [105.827, 32.456, 4000],
    [105.83, 32.456, 4000],
    [105.8234, 32.449, 4000],
  ]
  dataZ.forEach((bound) => {
    var entity = createAoiByCesium({
      color: new Cesium.Color(42 / 255, 165 / 255, 247 / 255, 0.5),
      show: true,
      positions: [105.809825, 32.446426, 10, 105.813169, 32.44911, 10, 105.815061, 32.447432, 10, 105.813891, 32.446415, 10, 105.811862, 32.442634, 10, 105.811501, 32.442315, 10, 105.809825, 32.446426, 10], // 格式需要为一维数组[114.112, 22.223, 40, 114.113, 22.224, 40]
      wallHeight: 500,
      hasHeight: true,
    })
    entity.tag = 'areaDsLines'
    if (!window.shiningWalls) {
      window.shiningWalls = []
    }
    window.shiningWalls.push(entity)
  })

  // 第二个电子围墙  53, 207, 87
  let dataA = [
    [105.818, 32.446155, 4000],
    [105.827, 32.456, 4000],
    [105.83, 32.456, 4000],
    [105.8234, 32.449, 4000],
  ]
  dataA.forEach((bound) => {
    var entity = createAoiByCesium({
      color: new Cesium.Color(53 / 255, 207 / 255, 87 / 255, 0.5),
      show: true,
      positions: [105.813239, 32.449161, 10, 105.815061, 32.447548, 10, 105.81871, 32.450701, 10, 105.817079, 32.452158, 10, 105.813239, 32.449161, 10], // 格式需要为一维数组[114.112, 22.223, 40, 114.113, 22.224, 40]
      wallHeight: 500,
      hasHeight: true,
    })
    entity.tag = 'areaDsLines'
    if (!window.shiningWalls) {
      window.shiningWalls = []
    }
    window.shiningWalls.push(entity)
  })
})
function clickFun(index) {
  if (index == 0) {
    // 望凤街
    window.parent.postMessage(
      {
        type: 'wangge', // 自定义事件名
        value: 'a', // 数据，只能是字符串
      },
      '*'
    )
  } else {
    // 中兴街
    window.parent.postMessage(
      {
        type: 'wangge', // 自定义事件名
        value: 'b', // 数据，只能是字符串
      },
      '*'
    )
  }
}
// 绘制电子围栏
function createAoiByCesium(option) {
  var color = option.color ? option.color : Cesium.Color.RED
  var maxHeight = option.maxHeight ? option.maxHeight : 10
  var minHeight = option.minHeight ? option.minHeight : 1
  var maxHeightArray = []
  var minHeightArray = []

  var c = document.createElement('canvas')
  c.width = 50
  c.height = 50
  var ctx = c.getContext('2d')

  var grd = ctx.createLinearGradient(0, 20, 0, 0)
  grd.addColorStop(0, 'rgba(' + color.red * 255 + ',' + color.green * 255 + ',' + color.blue * 255 + ',1)')
  grd.addColorStop(1, 'rgba(' + color.red * 255 + ',' + color.green * 255 + ',' + color.blue * 255 + ',0)')
  ctx.fillStyle = grd
  ctx.fillRect(0, 0, 50, 50)

  var config = null
  if (option.hasHeight) {
    var positions = []
    option.positions.forEach((x, index) => {
      if (index % 3 == 2) {
        minHeightArray.push(x)
        maxHeightArray.push(x + (isNaN(option.wallHeight) ? 1 : option.wallHeight))
      } else {
        positions.push(x)
      }
    })
    config = {
      wall: {
        show: option.show == false ? false : true,
        positions: Cesium.Cartesian3.fromDegreesArray(positions),
        material: new Cesium.ImageMaterialProperty({
          image: c,
          transparent: true,
        }),
        zIndex: 1000,
      },
    }
  } else {
    for (var i = 0; i < option.positions.length / 2; i++) {
      minHeightArray.push(minHeight)
      maxHeightArray.push(maxHeight)
    }
    config = {
      wall: {
        show: option.show == false ? false : true,
        positions: Cesium.Cartesian3.fromDegreesArray(option.positions),
        material: new Cesium.ImageMaterialProperty({
          image: c,
          transparent: true,
        }),
        zIndex: 1000,
      },
    }
  }
  config.wall.maximumHeights = maxHeightArray
  config.wall.minimumHeights = minHeightArray
  var entity1 = viewer.value.entities.add(config)

  entity1.wall.material.color = new Cesium.CallbackProperty(function (time, x) {
    var alp = 0.5 * Math.abs(Math.sin(new Date().getTime() / 500)) + 0.1
    return color.withAlpha(alp)
  }, false)
  return entity1
}
function getCurrentExtent() {
  // 范围对象
  var extent = {}
  // 得到当前三维场景
  var scene = viewer.value.scene
  // 得到当前三维场景的椭球体
  var ellipsoid = scene.globe.ellipsoid
  var canvas = scene.canvas
  // canvas左上角
  var car3_lt = viewer.value.camera.pickEllipsoid(new Cesium.Cartesian2(0, 0), ellipsoid)
  // canvas右下角
  var car3_rb = viewer.value.camera.pickEllipsoid(new Cesium.Cartesian2(canvas.width, canvas.height), ellipsoid)
  // 当canvas左上角和右下角全部在椭球体上
  if (car3_lt && car3_rb) {
    var carto_lt = ellipsoid.cartesianToCartographic(car3_lt)
    var carto_rb = ellipsoid.cartesianToCartographic(car3_rb)
    extent.xmin = Cesium.Math.toDegrees(carto_lt.longitude)
    extent.ymax = Cesium.Math.toDegrees(carto_lt.latitude)
    extent.xmax = Cesium.Math.toDegrees(carto_rb.longitude)
    extent.ymin = Cesium.Math.toDegrees(carto_rb.latitude)
  }
  // 当canvas左上角不在但右下角在椭球体上
  else if (!car3_lt && car3_rb) {
    var car3_lt2 = null
    var yIndex = 0
    do {
      // 这里每次10像素递加，一是10像素相差不大，二是为了提高程序运行效率
      yIndex <= canvas.height ? (yIndex += 10) : canvas.height
      car3_lt2 = viewer.value.camera.pickEllipsoid(new Cesium.Cartesian2(0, yIndex), ellipsoid)
    } while (!car3_lt2)
    var carto_lt2 = ellipsoid.cartesianToCartographic(car3_lt2)
    var carto_rb2 = ellipsoid.cartesianToCartographic(car3_rb)
    extent.xmax = Cesium.Math.toDegrees(carto_lt2.longitude)
    extent.ymax = Cesium.Math.toDegrees(carto_lt2.latitude)
    extent.xmin = Cesium.Math.toDegrees(carto_rb2.longitude)
    extent.ymin = Cesium.Math.toDegrees(carto_rb2.latitude)
  }
  // 获取高度
  extent.height = Math.ceil(viewer.value.camera.positionCartographic.height)
  extent.lon = Cesium.Math.toDegrees(viewer.value.camera.positionCartographic.longitude)
  extent.lat = Cesium.Math.toDegrees(viewer.value.camera.positionCartographic.latitude)
  extent.heading = Cesium.Math.toDegrees(viewer.value.camera.heading)
  extent.pitch = Cesium.Math.toDegrees(viewer.value.camera.pitch)
  extent.roll = Cesium.Math.toDegrees(viewer.value.camera.roll)
  //console.log("lon："+extent.lon+"--lat："+extent.lat+"--height："+extent.height+"--heading：" + extent.heading + "--pitch：" + extent.pitch
  //+ "--roll：" + extent.roll);
  //console.log('{"lon":"'+extent.lon+'","lat":"'+extent.lat+'","height":"'+extent.height+'","heading":"'+extent.heading+'","pitch":"'+extent.pitch+'","roll":"'+extent.roll+'"}');
  // console.log("{'lon':" + extent.lon + ",'lat':" + extent.lat + ",'height':" + extent.height + ",'heading':" + extent.heading + ",'pitch':" + extent.pitch + ",'roll':" + extent.roll + "}");

  return extent
}
onUnmounted(() => {
  // 清理postRender事件监听器
  if (viewer.value && viewer.value.scene && postRenderHandler.value) {
    viewer.value.scene.postRender.removeEventListener(postRenderHandler.value)
    postRenderHandler.value = null
  }

  // 销毁viewer
  if (viewer.value) {
    viewer.value.destroy()
    viewer.value = null
  }
})
</script>
<style scoped lang="scss">
.goback {
  width: 70px;
  height: 28px;
  background: url(/di01.png) no-repeat;
  // background-size: 100% 100%;
  z-index: 2;
  position: absolute;
  left: 150px;
  top: 25px;
  color: white;
  text-align: center;
  font-size: 14px;
  line-height: 28px;
  cursor: pointer;
}

.qiu {
  z-index: 3;
  position: absolute;
  right: 200px;
  top: 20px;
  cursor: pointer;
}

#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.left {
  z-index: 1;
  width: 442px;
  height: 93%;
  position: absolute;
  left: 0;
  top: 7%;

  .one {
    height: 24%;

    .one_dis {
      width: 100%;
      height: 185px;
      display: flex;
      // justify-content: space-between;

      .dis_ {
        color: #fff;
        width: 25%;
        text-align: center;

        .top {
          font-size: 34px;
          position: relative;
          top: 40px;
          font-family: DIN;
          font-weight: bold;
          color: #ffffff;
          line-height: 45px;

          background: linear-gradient(0deg, rgba(119, 244, 245, 0.99) 1.2939453125%);
        }

        .bottom {
          font-size: 16px;
        }
      }
    }
  }

  .two {
    height: 34%;

    .two_dis {
      height: 293px;
    }
  }

  .three {
    height: 39%;
  }
}

.right {
  z-index: 1;
  width: 442px;
  height: 93%;
  position: absolute;
  right: 0;
  top: 7%;

  .one {
    height: 23%;

    .one_dis {
      height: 175px;
    }
  }

  .two {
    height: 41%;

    .two_dis {
      height: 355px;
    }
  }

  .three {
    .three_dis {
      height: 287px;
    }
  }
}

.biao {
  width: 229px;
  height: 55px;
  position: absolute;
  left: 856px;
  top: 107px;
  background: url(/di04.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  color: white;
  font-size: 24px;
  text-align: center;
  line-height: 55px;
}

.title {
  height: 50px;
  background: url(/biaoti_di.png) no-repeat;
  background-size: cover;

  span {
    font-size: 26px;
    padding-left: 40px;
    font-weight: 600;
    line-height: 50px;
    color: #fff;
  }
}
</style>
