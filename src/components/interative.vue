<template>
  <div ref="age" id="age"></div>
  <div class="subtext">
    <span class="name">咨询总计：</span><span class="n1">{{ ziXunNum }}</span>
    &emsp;
    <span class="name">诉求总计：</span><span class="n2">{{ suQiuNum }}</span>
    &emsp;
    <span class="name">建议总计：</span><span class="n3">{{ jianYiNum }}</span>
    &emsp;
    <span class="name">表扬总计：</span><span class="n4">{{ biaoYangNum }}</span>
    &emsp;
    <span class="name">共计：</span><span class="n5">{{ allNum }}</span>
  </div>
</template>

<script setup>
import { getInteration } from '../api'
import * as echarts from 'echarts'
import { onMounted, ref, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'

// 声明组件要触发的事件
const emits = defineEmits(['increase'])
// 声明接收参数
const props = defineProps({
  dataInteraction: Object,
})
// const handelClick = () => {
//   console.log('触发子组件点击')
//   // 触发父组件事件
//   emits('increase')
// };
const route = useRoute()
const age = ref(null)
let ziXunNum = ref(0)
let suQiuNum = ref(0)
let jianYiNum = ref(0)
let biaoYangNum = ref(0)
let allNum = ref(0)
let dataInteraction = ref({})

onMounted(() => {
  setTimeout(() => {
    init()
  }, 200)
})
// 年龄构成图表
function init() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.age;
  getInteration({
    type: route.query.code ? (route.name == 'a' ? '2' : '3') : '1',
    code: route.query.code,
  }).then((res) => {
    ziXunNum.value = res.ziXunNum ? res.ziXunNum : 0
    suQiuNum.value = res.suQiuNum ? res.suQiuNum : 0
    biaoYangNum.value = res.biaoYangNum ? res.biaoYangNum : 0
    jianYiNum.value = res.jianYiNum ? res.jianYiNum : 0
    allNum = ziXunNum.value + suQiuNum.value + biaoYangNum.value + jianYiNum.value

    const myEcharts = echarts.init(age.value)
    var option
    var xData = ['养老', '党建', '民政', '就业', '安全', '纪检监察', '特殊群体', '卫计', '物业', '退役军人', '安全巡查', '其他']
    option = {
      backgroundColor: 'transparent',
      color: ['#3398DB'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            opacity: 0,
          },
        },
        backgroundColor: '#041a52',
        borderColor: '#65C1DC',
        textStyle: {
          color: 'white',
        },
      },
      legend: {
        left: 70,
        top: 150,
        data: ['咨询', '诉求', '建议', '表扬'],
        textStyle: {
          color: 'white',
          fontSize: 14,
        },
      },
      grid: {
        left: '2%',
        right: '2%',
        top: '5%',
        bottom: '50%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          gridIndex: 0,
          data: xData,
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: '#A4DAFF',
            },
          },
          axisLabel: {
            show: true,
            color: 'white',
            fontSize: 14,
          },
        },
      ],
      yAxis: {
        type: 'value',
        splitNumber: 4,
        interval: 10,
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        min: 0,
        // max: 30,
        axisLine: {
          lineStyle: {
            color: '#A4DAFF',
          },
        },
        axisLabel: {
          color: 'white',
          formatter: '{value}',
        },
      },
      series: [
        {
          name: '咨询',
          type: 'bar',
          barWidth: '15%',
          xAxisIndex: 0,
          yAxisIndex: 0,
          itemStyle: {
            color: '#0fa3f1',
          },
          data: res.ziXunCount,
        },
        {
          name: '诉求',
          type: 'bar',
          barWidth: '15%',
          xAxisIndex: 0,
          yAxisIndex: 0,
          itemStyle: {
            color: '#0f69f1',
          },
          data: res.suQiuCount,
        },
        {
          name: '建议',
          type: 'bar',
          barWidth: '15%',
          xAxisIndex: 0,
          yAxisIndex: 0,
          itemStyle: {
            color: '#30c6dc',
          },
          data: res.jianYiCount,
        },
        {
          name: '表扬',
          type: 'bar',
          barWidth: '15%',
          xAxisIndex: 0,
          yAxisIndex: 0,
          itemStyle: {
            color: '#4e88ff',
          },
          data: res.biaoYangCount,
        },
      ],
    }
    option && myEcharts.setOption(option)
  })
}
onUnmounted(() => {})
</script>

<style lang="scss" scoped>
#age {
  width: 940px;
  height: 250px;
  margin: 1% 0;
}

.subtext {
  text-align: right;
  height: 20px;
  width: 100%;
  position: relative;
  top: -110px;

  .name {
    font-size: 12px;
    color: #edf2fa;
  }

  .n1 {
    color: #0fa3f1;
    font-weight: bold;
    font-size: 16px;
  }

  .n2 {
    color: #0f69f1;
    font-weight: bold;
    font-size: 16px;
  }

  .n3 {
    color: #30dbad;
    font-weight: bold;
    font-size: 16px;
  }

  .n4 {
    color: #4e88ff;
    font-weight: bold;
    font-size: 16px;
  }

  .n5 {
    color: #f1728c;
    font-weight: bold;
    font-size: 16px;
  }
}
</style>
