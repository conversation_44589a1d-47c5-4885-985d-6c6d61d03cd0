<template>
  <div class="_box">
    <div class="single" @click="openBox(1)">
      <div class="top">{{ shiYe }}</div>
      <img src="/icon02.png" alt="" />
      <div class="bottom">事业单位(个)</div>
    </div>
    <div class="single" @click="openBox(2)">
      <div class="top">{{ sheHui }}</div>
      <img src="/icon02.png" alt="" />
      <div class="bottom">社会团体(个)</div>
    </div>
    <div class="single" @click="openBox(3)">
      <div class="top">{{ qiYe }}</div>
      <img src="/icon02.png" alt="" />
      <div class="bottom">企业(个)</div>
    </div>
    <div class="single" @click="openBox(4)">
      <div class="top">{{ geTi }}</div>
      <img src="/icon02.png" alt="" />
      <div class="bottom">个体户(个)</div>
    </div>
  </div>
</template>

<script setup>
import { government } from '../api'
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'

const emits = defineEmits(['dialogBussiness'])
// 声明接收参数
// const props = defineProps({
//   bussiness: Number,
// })
const route = useRoute()

onMounted(() => {
  setTimeout(() => {
    getEdata()
  }, 400)
})
function openBox(v) {
  switch (v) {
    case 1:
      emits('dialogBussiness', 4)
      getEdata(1)
      break
    default:
      break
  }
}
function getEdata(v) {
  let grid_name
  switch (route.query.code) {
    case '20':
      grid_name = '中兴街网格'
      break
    case '10':
      grid_name = '望凤街网格'
      break
    default:
      grid_name = ''
      break
  }
  government({
    limit: 15,
    // offset: (currentPage.value / 1 - 1) * 15,
    offset: 0,
    grid_name: grid_name,
  }).then((res) => {
    shiYe.value = res.data.count
  })
}
let shiYe = ref(0)
let sheHui = ref(6)
let qiYe = ref(109)
let geTi = ref(185)
</script>

<style lang="scss" scoped>
._box {
  display: flex;
  justify-content: space-around;
  width: 100%;
  height: 134px;

  .single {
    color: #fff;
    width: 25%;
    text-align: center;
    cursor: pointer;

    .top {
      width: 100%;
      text-align: center;
      font-size: 24px;
      position: relative;
      top: 20px;
      font-weight: bold;
      background-image: -webkit-linear-gradient(bottom, white, #77f4f5);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .bottom {
      font-size: 16px;
      font-weight: bold;
      margin-top: 15px;
    }
  }
}
</style>
