<template>
  <div ref="age" id="age"></div>
  <div class="subtext">
    <span class="name">未成年人：</span><span class="n1">{{ props.subObj.teenager }}</span
    ><span class="per">({{ props.subObj.teenager_percent }})</span>
    &emsp;
    <span class="name">青壮年：</span><span class="n2">{{ props.subObj.middle_aged }}</span
    ><span class="per">({{ props.subObj.middle_aged_percent }})</span>
    &emsp;
    <span class="name">老年人：</span><span class="n3">{{ props.subObj.old_aged }}</span
    ><span class="per">({{ props.subObj.old_aged_percent }})</span>
  </div>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted, getCurrentInstance, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex' // 引入useStore 方法
const store = useStore() // 该方法用于返回store 实例
const route = useRoute()
// 声明组件要触发的事件
const emits = defineEmits(['increase'])
// 声明接收参数
const props = defineProps({
  dataAge: Array,
  subObj: Object,
})
// //  点击事件
// const handelClick = () => {
//   console.log('触发子组件点击')
//   // 触发父组件事件
//   emits('increase')
// };
const age = ref(null)
onMounted(() => {
  setTimeout(() => {
    init()
  }, 1000)
})
// 年龄构成图表
function init() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.age;
  const myEcharts = echarts.init(age.value)
  var option
  var xData = [],
    yData = []
  // var min = 50
  props.dataAge.map(function (a, b) {
    xData.push(a.name)
    // if (a.value === 0) {
    //   yData.push(a.value + min)
    // } else {
    yData.push(a.value)
    // }
  })
  option = {
    backgroundColor: 'transparent',
    color: ['#3398DB'],
    // tooltip: {
    //   trigger: 'axis',
    //   axisPointer: {
    //     type: 'line',
    //     lineStyle: {
    //       opacity: 0
    //     }
    //   },
    //   formatter: function (prams) {
    //     return "合格率：" + prams[0].data + "%"
    //   }
    // },
    grid: {
      left: '5%',
      right: '5%',
      // bottom: '5%',
      top: '18%',
      // height: '85%',
      containLabel: true,
      // z: 22
    },
    xAxis: [
      {
        type: 'category',
        gridIndex: 0,
        data: xData,
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: 'white',
          },
        },
        axisLabel: {
          show: true,
          color: 'white',
          fontSize: 14,
        },
      },
    ],
    yAxis: {
      type: 'value',
      splitNumber: 4,
      interval: 1000,
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      min: 0,
      axisLine: {
        lineStyle: {
          color: 'white',
          width: 10,
        },
      },
      axisLabel: {
        color: 'white',
        formatter: '{value}',
      },
    },
    series: [
      {
        name: '人数',
        type: 'bar',
        barWidth: '30%',
        xAxisIndex: 0,
        yAxisIndex: 0,
        itemStyle: {
          barBorderRadius: [30, 30, 0, 0],
          // color: new echarts.graphic.LinearGradient(
          //   0, 0, 0, 1, [{
          //     offset: 0,
          //     color: '#00feff'
          //   },
          //   {
          //     offset: 0.5,
          //     color: '#027eff'
          //   },
          //   {
          //     offset: 1,
          //     color: '#0286ff'
          //   }
          // ]
          // )
          color: '#2FC6DC',
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)',
        },
        data: yData,
        label: {
          show: true,
          position: 'top',
          color: 'white',
        },
        zlevel: 11,
      },
    ],
  }

  option && myEcharts.setOption(option)
  myEcharts.getZr().on('click', (params) => {
    console.log('wwwwwwww',params);
    let pointInPixel = [params.offsetX, params.offsetY]
    if (myEcharts.containPixel('grid', pointInPixel)) {
      let xIndex = myEcharts.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[0]
      // console.log(xIndex)
      switch (xIndex) {
        case 1:
          // emits('dialogAge', 1)
           route.path == '/communities' ? store.dispatch('changeGridClosedAge', 1) : store.dispatch('changeCommunityAge', 1)
          break
        case 2:
          // emits('dialogAge', 2)
          route.path == '/communities' ? store.dispatch('changeGridClosedAge', 2) : store.dispatch('changeCommunityAge', 2)
          break
        case 3:
          // emits('dialogAge', 3)
          route.path == '/communities' ? store.dispatch('changeGridClosedAge', 3) : store.dispatch('changeCommunityAge', 3)
          break
        case 4:
          // emits('dialogAge', 4)
          route.path == '/communities' ? store.dispatch('changeGridClosedAge', 4) : store.dispatch('changeCommunityAge', 4)
          break
        case 5:
          // emits('dialogAge', 5)
          route.path == '/communities' ? store.dispatch('changeGridClosedAge', 5) : store.dispatch('changeCommunityAge', 5)
          break
        case 6:
          // emits('dialogAge', 6)
          route.path == '/communities' ? store.dispatch('changeGridClosedAge', 6) : store.dispatch('changeCommunityAge', 6)
          break
        default:
          // emits('dialogAge', 0)
          route.path == '/communities' ? store.dispatch('changeGridClosedAge', 0) : store.dispatch('changeCommunityAge', 0)
          break
      }
      if (route.path == '/communities') {
        store.dispatch('changeGridMask', true)
        store.dispatch('changeGridClosedAge', true)
        store.dispatch('changeGridUpdateData', true)
      } else {
        store.dispatch('changeCommunityMask', true)
        store.dispatch('changeCommunityClosedAge', true)
        store.dispatch('changeCommunityUpdateData', true)
      }
      
    }
  })
}
</script>

<style lang="scss" scoped>
#age {
  width: 500px;
  height: 220px;
  margin: 10px auto;
}

.subtext {
  text-align: center;
  height: 20px;
  width: 100%;
  position: relative;
  top: -230px;

  .name {
    font-size: 12px;
    color: #edf2fa;
  }

  .n1 {
    color: #f1728c;
    font-weight: bold;
    font-size: 16px;
  }

  .n2 {
    color: #f4983c;
    font-weight: bold;
    font-size: 16px;
  }

  .n3 {
    color: #0fa3f1;
    font-weight: bold;
    font-size: 16px;
  }

  .per {
    color: white;
    font-size: 14px;
  }
}
</style>
