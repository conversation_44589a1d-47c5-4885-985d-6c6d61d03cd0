<template>
  <div ref="student" id="student"></div>
  <div ref="soldier" id="soldier"></div>
  <div class="pie">
    <div ref="person" id="person" @click="showTable1"></div>
    <div ref="special" id="special" @click="showTable2"></div>
    <div ref="important" id="important" @click="showTable3"></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted, getCurrentInstance, ref } from 'vue'
import { useStore } from 'vuex' // 引入useStore 方法
import { useRoute } from 'vue-router'

const route = useRoute()
const store = useStore() // 该方法用于返回store 实例
// 声明组件要触发的事件
const emits = defineEmits(['dialog'])
// 声明接收参数
const props = defineProps({
  dataStudent: Array,
  dataSolier: Array,
  dataPerson: Array,
  dataImportant: Array,
  dataSpecial: Array,
})
// //  点击事件
// const handelClick = () => {
//   console.log('触发子组件点击')
//   // 触发父组件事件
//   emits('increase')
// };
const student = ref(null)
const soldier = ref(null)
const person = ref(null)
const special = ref(null)
const important = ref(null)
onMounted(() => {
  setTimeout(() => {
    init2()
    init3()
    init4()
    init5()
    init6()
  }, 1000)
})
// 学生构成图表
function init2() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.student;
  const myEcharts = echarts.init(student.value)
  const total = ref(0)
  total.value = props.dataStudent.reduce((pre, v) => {
    return (pre += v.value)
  }, 0)

  function getArrByKey(data, k) {
    let key = k || 'value'
    let res = []
    if (data) {
      data.forEach(function (t) {
        res.push(t[key])
      })
    }
    return res
  }
  // function getSymbolData(data) {
  //   let arr = []
  //   for (var i = 0; i < data.length; i++) {
  //     arr.push({
  //       value: data[i].value,
  //       symbolPosition: 'end',
  //     })
  //   }
  //   return arr
  // }
  var option = {
    title: {
      text: '学生构成',
      textStyle: {
        color: '#fff',
      },
      left: 190,
      top: 5,
    },
    backgroundColor: 'transparent',
    grid: {
      top: '20%',
      bottom: -15,
      left: 0,
      containLabel: true,
    },
    xAxis: {
      show: false,
    },
    yAxis: [
      {
        triggerEvent: true,
        show: true,
        inverse: true,
        data: getArrByKey(props.dataStudent, 'name'),
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
          interval: 0,
          color: 'red',
          align: 'left',
          margin: 80,
          fontSize: 13,
          formatter: function (value, index) {
            return '{title|' + value + '}'
          },
          rich: {
            title: {
              width: 165,
            },
          },
        },
      },
      {
        triggerEvent: true,
        show: true,
        inverse: true,
        data: getArrByKey(props.dataStudent, 'name'),
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        // 右侧的数值
        axisLabel: {
          interval: 0,
          shadowOffsetX: '-20px',
          color: '#04BAFF',
          fontWeight: 'bold',
          align: 'right',
          verticalAlign: 'bottom',
          lineHeight: 30,
          fontSize: 14,
          fontStyle: 'italic',
          formatter: function (value, index) {
            return ((props.dataStudent[index].value / total.value) * 100).toFixed(1) + '%'
          },
        },
      },
    ],
    series: [
      {
        name: '条',
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          borderRadius: 30,
        },
        barBorderRadius: 30,
        yAxisIndex: 0,
        data: props.dataStudent,
        barWidth: 9,
        // align: left,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: '#1486DB',
              },
              {
                offset: 1,
                color: '#00F0FF',
              },
            ],
            false
          ),
          barBorderRadius: 10,
          barBorderRadius: 4,
        },
        label: {
          show: true,
          color: '#fff',
          position: [0, '-20px'],
          textStyle: {
            fontSize: 14,
          },
          formatter: function (a, b) {
            return ` ${a.name}  {hou|${a.value}} 人`
          },
          rich: {
            hou: {
              fontWeight: 'bold',
              fontSize: 16,
            },
          },
        },
      },
    ],
  }
  option && myEcharts.setOption(option)
  if(route.path == '/communities'){
      myEcharts.getZr().on('click', (params) => {
      if (params.target) {
        switch (params.target.__dataIndex) {
          case 1:
            // emits('dialogStudent', 2)
            store.dispatch('changeGridStudent', 2)
            break
          case 2:
            // emits('dialogStudent', 3)
            store.dispatch('changeGridStudent', 3)
            break
          case 3:
            // emits('dialogStudent', 4)
            store.dispatch('changeGridStudent', 4)
            break
          default:
            // emits('dialogStudent', 1)
            store.dispatch('changeGridStudent', 1)
            break
        }
      } else {
        // emits('dialogStudent', 1)
        store.dispatch('changeGridStudent', 1)
      }
      
        store.dispatch('changeGridMask', true)
        store.dispatch('changeGridClosedStudent', true)
        store.dispatch('changeGridUpdateStudent', true)
      
      
    })
  }else{
    myEcharts.getZr().on('click', (params) => {
      if (params.target) {
        console.log('陆游',route.path,params.target);
        switch (params.target.__dataIndex) {
          case 1:
            // emits('dialogStudent', 2)
            store.dispatch('changeCommunityStudent', 2)
            break
          case 2:
            // emits('dialogStudent', 3)
            store.dispatch('changeCommunityStudent', 3)
            break
          case 3:
            // emits('dialogStudent', 4)
            store.dispatch('changeCommunityStudent', 4)
            break
          default:
            // emits('dialogStudent', 1)
            store.dispatch('changeCommunityStudent', 1)
            break
        }
      } else {
        // emits('dialogStudent', 1)
        store.dispatch('changeCommunityStudent', 1)
      }
      
        store.dispatch('changeCommunityMask', true)
        store.dispatch('changeCommunityClosedStudent', true)
        store.dispatch('changeCommunityUpdateStudent', true)
      
    })
  }
  
}

// 重点监管
function init3() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.student;
  props.dataImportant.map((v) => {
    v.value = v.num
  })
  const myEcharts = echarts.init(soldier.value)
  const total = ref(0)
  total.value = props.dataImportant.reduce((pre, v) => {
    return (pre += v.value)
  }, 0)

  function getArrByKey(data, k) {
    let key = k || 'value'
    let res = []
    if (data) {
      data.forEach(function (t) {
        res.push(t[key])
      })
    }
    return res
  }
  // function getSymbolData(data) {
  //   let arr = []
  //   for (var i = 0; i < data.length; i++) {
  //     arr.push({
  //       value: data[i].value,
  //       symbolPosition: 'end',
  //     })
  //   }
  //   return arr
  // }
  var option = {
    title: {
      text: '重点监管人群',
      textStyle: {
        color: '#fff',
      },
      left: 190,
      top: 5,
    },
    backgroundColor: 'transparent',
    grid: {
      top: '20%',
      bottom: -15,
      left: 0,
      containLabel: true,
    },
    xAxis: {
      show: false,
    },
    yAxis: [
      {
        triggerEvent: true,
        show: true,
        inverse: true,
        data: getArrByKey(props.dataImportant, 'name'),
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
          interval: 0,
          color: 'red',
          align: 'left',
          margin: 80,
          fontSize: 13,
          formatter: function (value, index) {
            return '{title|' + value + '}'
          },
          rich: {
            title: {
              width: 165,
            },
          },
        },
      },
      {
        triggerEvent: true,
        show: true,
        inverse: true,
        data: getArrByKey(props.dataImportant, 'name'),
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        // 右侧的数值
        axisLabel: {
          interval: 0,
          shadowOffsetX: '-20px',
          color: '#04BAFF',
          fontWeight: 'bold',
          align: 'right',
          verticalAlign: 'bottom',
          lineHeight: 30,
          fontSize: 14,
          fontStyle: 'italic',
          formatter: function (value, index) {
            return ((props.dataImportant[index].value / total.value) * 100).toFixed(1) + '%'
          },
        },
      },
    ],
    series: [
      {
        name: '条',
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          borderRadius: 30,
        },
        barBorderRadius: 30,
        yAxisIndex: 0,
        data: props.dataImportant,
        barWidth: 9,
        // align: left,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: '#1486DB',
              },
              {
                offset: 1,
                color: '#00F0FF',
              },
            ],
            false
          ),
          barBorderRadius: 10,
          barBorderRadius: 4,
        },
        label: {
          show: true,
          color: '#fff',
          position: [0, '-20px'],
          textStyle: {
            fontSize: 14,
          },
          formatter: function (a, b) {
            return ` ${a.name}  {hou|${a.value}} 人`
          },
          rich: {
            hou: {
              fontWeight: 'bold',
              fontSize: 16,
            },
          },
        },
      },
    ],
  }
  option && myEcharts.setOption(option)
  myEcharts.getZr().on('click', (params) => {
    if (params.target) {
      console.log(params.target)
      switch (params.target.__dataIndex) {
        case 1:
          // emits('dialogSolier', 3)

          route.path == '/communities' ? store.dispatch('changeGridSolier', 3) : store.dispatch('changeCommunitySolier', 3) 
          break
        case 2:
          // emits('dialogSolier', 1)
          route.path == '/communities' ? store.dispatch('changeGridSolier', 1) : store.dispatch('changeCommunitySolier', 1)
          break
        case 3:
          // emits('dialogSolier', 1)
          route.path == '/communities' ? store.dispatch('changeGridSolier', 1) : store.dispatch('changeCommunitySolier', 1)
          break
        default:
          // emits('dialogSolier', 2)
          route.path == '/communities' ? store.dispatch('changeGridSolier', 2) : store.dispatch('changeCommunitySolier', 2)
          break
      }
    } else {
      console.log(params.target)
      // emits('dialogSolier', 1)
      route.path == '/communities' ? store.dispatch('changeGridSolier', 1) : store.dispatch('changeCommunitySolier', 1)
    }
    if (route.path == '/communities') {
      store.dispatch('changeGridMask', true)
      store.dispatch('changeGridClosedSolier', true)
      store.dispatch('changeGridUpdateSolier', true)
    } else {
      store.dispatch('changeCommunityMask', true)
      store.dispatch('changeCommunityClosedSolier', true)
      store.dispatch('changeCommunityUpdateSolier', true)
    }
    
  })
}
// 社区名人构成图表
function init4() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.person;
  const myEcharts = echarts.init(person.value)
  const colorList = [
    [
      { offset: 0, color: 'rgba(23, 77, 216, 1)' },
      { offset: 1, color: 'rgba(23, 77, 216, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(39, 89, 217, 1)' },
      { offset: 1, color: 'rgba(39, 89, 217, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(29, 100, 242, 1)' },
      { offset: 1, color: 'rgba(29, 100, 242, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(54, 116, 242, 1)' },
      { offset: 1, color: 'rgba(54, 116, 242, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(78, 136, 255, 1)' },
      { offset: 1, color: 'rgba(82, 139, 255, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(255, 205, 94, 1)' },
      { offset: 1, color: 'rgba(253, 203, 94, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(205, 94, 244, 1)' },
      { offset: 1, color: 'rgba(147, 42, 225, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(245, 146, 106, 1)' },
      { offset: 1, color: 'rgba(221, 75, 52, 1)' },
    ],
  ]
  const echartData = props.dataPerson.map((v, i) => ({
    name: v.name,
    value: v.num,
    itemStyle: {
      color: { type: 'linear', colorStops: colorList[i] },
    },
  }))
  var option = {
    title: {
      text: '社区名人',
      left: 'center',
      textStyle: {
        color: 'white',
      },
      top: 6,
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c} ({d}%)',
      backgroundColor: '#06111f',
      textStyle: {
        color: 'white',
      },
    },
    series: [
      {
        name: 'Area Mode',
        type: 'pie',
        radius: [30, 80],
        center: ['50%', '50%'],
        roseType: 'area',
        label: {
          position: 'inside',
          textStyle: {
            color: 'white',
            fontWeight: 'bold',
          },
          formatter: function (params) {
            return params.value + '\n' + params.name
          },
        },
        data: echartData,
      },
    ],
  }
  option && myEcharts.setOption(option)
}
// 特殊人群构成图表
function init5() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.special;
  const myEcharts = echarts.init(special.value)
  var option
  const data = props.dataSpecial.map((v, i) => ({
    name: v.name,
    value: v.num,
  }))

  option = {
    backgroundColor: 'transparent',
    title: {
      text: '特殊人群',
      textStyle: {
        color: '#fff',
      },
      left: 90,
      top: 7,
    },
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)',
      backgroundColor: '#00060b',
      textStyle: {
        color: 'white',
      },
      borderColor: '#00060b',
    },
    series: [
      {
        type: 'pie',
        selectedMode: 'single',
        radius: ['25%', '55%'],
        z: 1,
        left: 20,
        color: ['#2759D9', '#4E88FF', '#1d64f2', '#78a4ff', '#174dd8'],
        label: {
          position: 'inner',
          formatter: '{c}\n{b}',
          textStyle: {
            color: '#fff',
            fontWeight: 'bold',
            fontSize: 14,
          },
        },
        labelLine: {
          show: false,
        },
        data: data,
      },
      {
        type: 'pie',
        radius: ['0', '30%'],
        z: 2,
        left: 20,
        itemStyle: {
          color: 'rgba(0,0,0,.3)',
        },
        label: {
          show: false,
        },
        data: data,
      },
    ],
  }

  option && myEcharts.setOption(option)
}
// 重点监控人群构成图表
function init6() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.important;
  const myEcharts = echarts.init(important.value)
  const data1 = props.dataSolier.map((v) => {
    return {
      value: v.value,
      name: v.name,
    }
  })
  const data2 = props.dataImportant
  var option = {
    title: {
      text: '军人构成',
      textStyle: {
        color: '#fff',
      },
      left: 60,
      top: 7,
    },
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.9)',
      formatter: function (params) {
        return params.marker + '<span style="color:white">' + params.data['name'] + '\n' + params.data['value'] + '</span>'
      },
    },
    series: [
      {
        name: '',
        type: 'pie',
        hoverAnimation: false,
        legendHoverLink: false,
        cursor: 'default',
        right: 20,
        radius: ['32%', '50%'],
        center: ['50%', '50%'],
        color: ['rgba(54, 186, 255,0.2)', 'rgba(15, 105, 241, 0.2)', 'rgba(48, 198, 220, 0.2)', 'rgba(78, 136, 255, 0.2)', 'rgba(15, 163, 241, 0.2)'],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        zlevel: 1,
        itemStyle: {
          borderColor: '#0a1a2a',
          ellipsis: {
            borderColor: '#0a1a2a',
          },
        },
        tooltip: {
          show: false,
        },
        data: data1,
      },
      {
        name: '',
        type: 'pie',
        zlevel: 2,
        right: 20,
        cursor: 'default',
        hoverAnimation: false,
        legendHoverLink: false,
        radius: ['37%', '50%'],
        center: ['50%', '50%'],
        color: ['rgba(54, 186, 255, 0.5)', 'rgba(15, 105, 241, 0.5)', 'rgba(48, 198, 220, 0.5)', 'rgba(78, 136, 255, 0.5)', 'rgba(15, 163, 241, 0.5)'],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        itemStyle: {
          borderColor: '#0a1a2a',
          ellipsis: {
            borderColor: '#0a1a2a',
          },
        },
        tooltip: {
          show: false,
        },
        data: data1,
      },
      {
        name: 'title',
        type: 'pie',
        zlevel: 3,
        right: 20,
        hoverAnimation: false,
        legendHoverLink: false,
        radius: ['43%', '58%'],
        center: ['50%', '50%'],
        color: ['rgba(54, 186, 255, 1)', 'rgba(15, 105, 241, 1)', 'rgba(48, 198, 220, 1)', 'rgba(78, 136, 255, 1)', 'rgba(15, 163, 241, 1)'],
        label: {
          show: true,
          formatter: (params) => {
            return `
              ${params.data.value}
              ${params.name}
              `
          },
          // padding: [0, -40],
          textStyle: {
            // lineHeight: 20,
            color: 'white',
            fontWeight: 'bold',
          },
          position: 'inside',
        },
        data: data1,
      },
    ],
  }
  option && myEcharts.setOption(option)
}
// 社区名人表格弹窗
function showTable1() {
  emits('dialog', 1)
  if(route.path == '/communities'){
    store.dispatch('changeGridNo', 1)
    store.dispatch('changeGridMask', true)
    store.dispatch('changeGridClosed1', true)
  }else{
    store.dispatch('changeCommunityNo', 1)
    store.dispatch('changeCommunityMask', true)
    store.dispatch('changeCommunityClosed1', true)
  }
  
}
// 特殊人群表格弹窗
function showTable2() {
  emits('dialog', 2)
  if(route.path == '/communities'){
    store.dispatch('changeGridNo', 2)
    store.dispatch('changeGridMask', true)
    store.dispatch('changeGridClosed1', true)
  }else{
    store.dispatch('changeCommunityNo', 2)
    store.dispatch('changeCommunityMask', true)
    store.dispatch('changeCommunityClosed1', true)
  }
  
}
// 重点监控人群表格弹窗
function showTable3() {
  emits('dialog', 3)
  if(route.path == '/communities'){
    store.dispatch('changeGridNo', 3)
    store.dispatch('changeGridMask', true)
    store.dispatch('changeGridClosed1', true)
  }else{
    store.dispatch('changeCommunityNo', 3)
    store.dispatch('changeCommunityMask', true)
    store.dispatch('changeCommunityClosed1', true)
  }
  
}
</script>

<style lang="scss" scoped>
.subtext {
  text-align: center;
  height: 20px;
  width: 100%;
  position: relative;
  top: -240px;

  .name {
    font-size: 12px;
    color: #edf2fa;
  }

  .n1 {
    color: #f1728c;
    font-weight: bold;
    font-size: 16px;
  }

  .n2 {
    color: #f4983c;
    font-weight: bold;
    font-size: 16px;
  }

  .n3 {
    color: #0fa3f1;
    font-weight: bold;
    font-size: 16px;
  }

  .per {
    color: white;
    font-size: 14px;
  }
}

#student {
  width: 550px;
  height: 220px;
  position: relative;
  left: 6%;
}

#soldier {
  width: 550px;
  height: 220px;
  position: relative;
  left: 6%;
  // top: -51%;
}

.pie {
  width: 100%;
  display: flex;
  position: relative;
  top: 5%;
  right: 2%;

  #person {
    width: 240px;
    height: 240px;
    position: absolute;
    left: -7%;
  }

  #special {
    width: 240px;
    height: 240px;
    position: absolute;
    left: 24%;
  }

  #important {
    width: 240px;
    height: 240px;
    position: absolute;
    top: -2%;
    right: -6%;
  }
}
</style>
