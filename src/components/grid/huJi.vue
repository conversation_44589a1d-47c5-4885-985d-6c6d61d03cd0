<template>
  <div class="dialog">
    <div class="title">
      <span>户籍情况({{ huJiRenKou + waiLaiRenKou }}人)</span>
    </div>
    <div class="tabs">
      <div class="tab">
        <div class="single" @click="changeIs(3)">
          <div :style="is == 3 ? { 'font-weight': 'bold' } : {}">户籍人口({{ huJiRenKou }}人)</div>
          <img src="/selected.png" alt="" v-show="is == 3" />
        </div>
        <div class="single" @click="changeIs(1)">
          <div :style="is == 1 ? { 'font-weight': 'bold' } : {}">外来人口({{ waiLaiRenKou }}人)</div>
          <img src="/selected.png" alt="" v-show="is == 1" />
        </div>
      </div>
      <div class="close" @click="handelClick">
        <img src="/goback.png" alt="" />
        <span> 返回</span>
      </div>
    </div>
    <table border="0">
      <thead>
        <th>序号</th>
        <th @click="searchName">
          姓名
          <img src="../../assets/search.png" class="searchs" />
        </th>
        <th>身份证号码</th>
        <th>性别</th>
        <th>电话</th>
        <!-- <th>家庭关系</th> -->
        <th>小区</th>
        <th>住址</th>
        <th>职业</th>
        <th>户籍情况</th>
        <!-- <th @click="searchName">政治面貌<img src="../../assets/search.png" class="searchs" /></th> -->
      </thead>
      <tbody class="tb1" v-if="tableData.length">
        <tr v-for="(v, index) in tableData" :key="v.id">
          <td>{{ index + 1 }}</td>
          <td>{{ v.name }}</td>
          <td>{{ v.id_card }}</td>
          <td>{{ v.gender }}</td>
          <td>{{ v.phone }}</td>
          <!-- <td>{{ v.family_relation }}</td> -->
          <td>{{ v.address }}</td>
          <td>{{ v.room }}</td>
          <td>{{ v.occupation }}</td>
          <td>{{ v.domicile_from }}人口</td>
          <!-- <td>{{ v.political ? v.political : '群众' }}</td> -->
        </tr>
      </tbody>
      <tbody v-else>
        <p class="zan">暂无数据！</p>
      </tbody>
    </table>
    <div class="pagination-block">
      <el-pagination layout="prev, pager, next" :total="total" style="--el-pagination-font-size: 16px; --el-pagination-button-disabled-bg-color: none; --el-pagination-bg-color: none; --el-pagination-button-color: white" :current-page="currentPage" :page-size="pageSize" @current-change="handleCurrentChange" />
    </div>
    <div class="searchBox" v-show="showSearch">
      <div class="ipt">
        <div>
          <div class="label">姓&ensp;&emsp;名</div>
          <input type="text" v-model="userName" placeholder="请输入姓名" class="ipt1" />
        </div>
        <!-- <div style="margin-top: 10px">
          <div class="boxs">
            <div class="label">政治面貌</div>
            <div class="selects" @click="inputSelect">{{ userType }}</div>
          </div>
          <div class="options" v-show="showOptions">
            <div v-for="item in optionList" @click.native="getOption(item)">{{ item.name }}</div>
          </div>
        </div> -->
      </div>
      <div class="btns">
        <div @click="searchData">搜索</div>
        <div @click="searchCancel">取消</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAllPeopleHuJi, getAllPersonal } from '../../api'
import * as echarts from 'echarts'
import { ref, onMounted, reactive, getCurrentInstance } from 'vue'
import { useRoute } from 'vue-router'
const emits = defineEmits(['closeHuJi'])
// 声明接收参数
const props = defineProps({})
// //  点击事件
const handelClick = () => {
  console.log('触发子组件点击')
  // 触发父组件事件
  emits('closeHuJi')
}
const route = useRoute()
let is = ref(3)
let showSearch = ref(false)
let tableData = ref([])
let huJiRenKou = ref(0)
let waiLaiRenKou = ref(0)
let optionList = ref([{ name: '群众' }, { name: '中共党员' }, { name: '全部' }])
let userName = ref('')
let userType = ref('全部')
let showOptions = ref(false)

let total = ref(1)
let currentPage = ref(1)
let pageSize = ref(15)

function changeIs(val) {
  is.value = val
  currentPage.value = 1
  userType.value = '全部'
  getAllPersonals()
}
function searchName() {
  showSearch.value = true
}
// 搜索
function searchData() {
  // console.log(userName.value, userType.value)
  currentPage.value = 1
  setTimeout(() => {
    getAllPersonals()
    searchCancel()
  }, 300)
}
function searchCancel() {
  showSearch.value = false
  // userType.value = '全部'
  userName.value = ''
}
function inputSelect() {
  showOptions.value = !showOptions.value
}
function getOption(v) {
  userType.value = v.name
  showOptions.value = !showOptions.value
}
// 分页
function handleCurrentChange(v) {
  currentPage.value = v
  getAllPersonals()
}
function getAllPeoples() {
  getAllPeopleHuJi({
    type: route.name == 'a' ? '2' : '3',
    code: route.query.code,
  }).then((res) => {
    huJiRenKou.value = res.data.huJiRenKou
    waiLaiRenKou.value = res.data.waiLaiRenKou
  })
}
function getAllPersonals() {
  getAllPersonal({
    type: route.name == 'a' ? '2' : '3',
    code: route.query.code,
    personnel_type: is.value,
    limit: 15,
    offset: (currentPage.value / 1 - 1) * 15,
    name: userName.value,
    political_outlook: userType.value == '全部' ? '' : userType.value,
  }).then((res) => {
    total.value = res.data.count
    tableData.value = res.data.rows
  })
}
onMounted(() => {
  getAllPeoples()
  getAllPersonals()
})
</script>

<style scoped lang="scss">
.zan {
  position: absolute;
  font-size: 16px;
  color: white;
  top: 30%;
  width: 100%;
  text-align: center;
}
.dialog {
  width: 100%;
  height: 740px;
  background-color: #07192a;
  position: relative;

  .title {
    width: 100%;
    text-align: center;
    height: 38px;
    outline: 1px solid #0c3f7e;
    background: url(/title.png) no-repeat;
    background-position: 50%;
    font-size: 20px;
    font-weight: bold;
    color: #4a97f1;

    span {
      vertical-align: middle;
    }
  }

  .tabs {
    height: 53px;
    position: relative;
    outline: 1px solid #0c3f7e;

    .tab {
      width: 552px;
      text-align: center;
      font-size: 18px;
      // font-weight: bold;
      color: #ffffff;
      display: flex;

      .single {
        width: 200px;
        height: 35px;
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        cursor: pointer;
      }
    }

    .close {
      position: absolute;
      color: #4a97f1;
      cursor: pointer;
      right: 19px;
      top: 35%;
      font-size: 16px;
    }
  }
}
// 表格
table {
  width: 98%;
  height: 80%;
  margin: 10px auto;
  border: 2px solid #0c3f7e;
  border-collapse: collapse;
}

table thead {
  background-color: #0a2846;
  height: 34px;

  th {
    font-size: 18px;
    color: #4a97f1;
  }

  th:nth-child(1) {
    width: 40px;
  }

  th:nth-child(2) {
    cursor: pointer;
    width: 100px;
  }

  th:nth-child(3) {
    width: 180px;
  }

  th:nth-child(4) {
    width: 50px;
  }

  th:nth-child(5) {
    width: 150px;
  }
  th:nth-child(6) {
    width: 150px;
  }

  th:nth-child(7) {
    width: 150px;
  }

  th:nth-child(8) {
    width: 150px;
  }
  th:nth-child(10) {
    cursor: pointer;
  }
}

table tbody {
  max-height: 570px;
  display: block;
  overflow-y: auto;

  tr:nth-child(even) {
    background: #0e2947;
    height: 38px;

    td {
      text-align: center;
      color: white;
      font-size: 16px;
    }

    td:nth-child(1) {
      width: 40px;
    }

    td:nth-child(2) {
      width: 100px;
    }

    td:nth-child(3) {
      width: 180px;
    }

    td:nth-child(4) {
      width: 50px;
    }

    td:nth-child(5) {
      width: 150px;
    }
    td:nth-child(6) {
      width: 150px;
    }

    td:nth-child(7) {
      width: 150px;
    }

    td:nth-child(8) {
      width: 150px;
    }
  }

  tr:nth-child(odd) {
    height: 38px;

    td {
      text-align: center;
      color: white;
      font-size: 16px;
    }

    td:nth-child(1) {
      width: 40px;
    }

    td:nth-child(2) {
      width: 100px;
    }

    td:nth-child(3) {
      width: 180px;
    }

    td:nth-child(4) {
      width: 50px;
    }

    td:nth-child(5) {
      width: 150px;
    }
    td:nth-child(6) {
      width: 150px;
    }

    td:nth-child(7) {
      width: 150px;
    }

    td:nth-child(8) {
      width: 150px;
    }
  }
}

table thead,
table tbody tr {
  width: 100%;
  display: table;
  table-layout: fixed;
}

table thead {
  width: calc(100% - 3px);
}

table .tb1::-webkit-scrollbar {
  width: 10px !important;
}

::-webkit-scrollbar {
  width: 10px !important;
  height: 16px !important;
  background-color: rgba(21, 40, 79, 0.26);
  border-radius: 10px;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: rgba(21, 40, 79, 0.26);
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #234674;
}
.searchs {
  width: 20px;
}
.searchBox {
  width: 500px;
  height: 140px;
  background-color: black;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 30%;
  font-size: 20px;
  border-radius: 20px;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  color: white;
  .label {
    display: inline-block;
    width: 100px;
    text-align: center;
  }

  .btns {
    display: flex;
    div {
      width: 100px;
      text-align: center;
      background-color: #4a97f1;
      height: 50px;
      line-height: 50px;
      border-radius: 15px;
      margin: 0 20px;
      cursor: pointer;
    }
  }
}
.ipt1 {
  width: 200px;
  height: 40px;
  background: transparent;
  border: 2px solid white;
  font-size: 18px;
  color: white;
  text-align: center;
}
.pagination-block {
  position: absolute;
  right: 0;
  bottom: 2.5px;
}
.options {
  width: 200px;
  height: 240px;
  background-color: #1e1e1e;
  position: absolute;
  text-align: center;
  left: 200px;
  div {
    margin: 5px 0;
    cursor: pointer;
    padding: 8px 0;
  }
  div:hover {
    background-color: #0a2846;
  }
}
.boxs {
  display: flex;
  align-items: center;
}
.selects {
  width: 205px;
  height: 40px;
  background: transparent;
  border: 2px solid white;
  font-size: 18px;
  color: white;
  text-align: center;
  line-height: 40px;
  border-radius: 5px;
}
</style>
