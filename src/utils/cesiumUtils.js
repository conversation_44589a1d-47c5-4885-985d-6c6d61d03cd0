/**
 * Cesium 工具函数
 * 用于统一处理 Cesium 相关的初始化和清理逻辑
 */

/**
 * 安全地销毁 Cesium Viewer
 * @param {Object} viewer - Cesium Viewer 实例
 */
export function destroyViewer(viewer) {
  if (viewer && typeof viewer.destroy === 'function') {
    try {
      viewer.destroy()
    } catch (error) {
      console.warn('Error destroying Cesium viewer:', error)
    }
  }
}

/**
 * 安全地销毁 ScreenSpaceEventHandler
 * @param {Object} handler - Cesium ScreenSpaceEventHandler 实例
 */
export function destroyEventHandler(handler) {
  if (handler && typeof handler.destroy === 'function') {
    try {
      handler.destroy()
    } catch (error) {
      console.warn('Error destroying Cesium event handler:', error)
    }
  }
}

/**
 * 安全地移除事件监听器
 * @param {Object} viewer - Cesium Viewer 实例
 * @param {Function} eventHandler - 事件处理函数
 */
export function removePostRenderListener(viewer, eventHandler) {
  if (viewer && viewer.scene && viewer.scene.postRender && eventHandler) {
    try {
      viewer.scene.postRender.removeEventListener(eventHandler)
    } catch (error) {
      console.warn('Error removing postRender listener:', error)
    }
  }
}

/**
 * 完整的 Cesium 清理函数
 * @param {Object} options - 清理选项
 * @param {Object} options.viewer - Cesium Viewer 实例
 * @param {Object} options.handler - ScreenSpaceEventHandler 实例
 * @param {Function} options.postRenderHandler - postRender 事件处理函数
 * @param {Object} options.timer - 定时器引用
 */
export function cleanupCesium(options = {}) {
  const { viewer, handler, postRenderHandler, timer } = options
  
  // 清理 postRender 事件监听器
  if (postRenderHandler) {
    removePostRenderListener(viewer, postRenderHandler)
  }
  
  // 清理事件处理器
  if (handler) {
    destroyEventHandler(handler)
  }
  
  // 清理定时器
  if (timer) {
    clearTimeout(timer)
  }
  
  // 销毁 viewer
  if (viewer) {
    destroyViewer(viewer)
  }
}

/**
 * 安全地加载 3D 瓦片集
 * @param {Object} viewer - Cesium Viewer 实例
 * @param {string} url - 瓦片集 URL
 * @param {Object} options - 瓦片集选项
 * @returns {Promise} 返回瓦片集加载 Promise
 */
export function loadTileset(viewer, url, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      const tileset = new Cesium.Cesium3DTileset({
        url,
        ...options
      })
      
      tileset.readyPromise.then(() => {
        console.log('3D Tileset loaded successfully:', url)
        resolve(tileset)
      }).catch((error) => {
        console.warn('3D Tileset failed to load:', error)
        // 移除失败的瓦片集
        if (viewer && viewer.scene) {
          viewer.scene.primitives.remove(tileset)
        }
        reject(error)
      })
      
      // 添加到场景
      if (viewer && viewer.scene) {
        viewer.scene.primitives.add(tileset)
      }
      
    } catch (error) {
      console.warn('Failed to create 3D Tileset:', error)
      reject(error)
    }
  })
}

/**
 * 创建安全的 postRender 事件处理器
 * @param {Function} callback - 回调函数
 * @param {Object} viewer - Cesium Viewer 实例
 * @returns {Function} 安全的事件处理器
 */
export function createSafePostRenderHandler(callback, viewer) {
  return () => {
    // 检查 viewer 和相关对象是否仍然有效
    if (!viewer || !viewer.scene || !viewer.scene.canvas) {
      return
    }
    
    try {
      callback()
    } catch (error) {
      console.warn('Error in postRender handler:', error)
    }
  }
}
