<template>
  <div class="goback" @click="$router.go(-1)" style="font-size: 12px">
    <span class="backText">&emsp;返回</span>
  </div>
  <div class="qiu" @click="showAll">
    <img src="/zhankai.png" v-show="$store.state.show.zhan" alt="" />
    <img src="/shousuo.png" v-show="!$store.state.show.zhan" alt="" />
  </div>
  <div class="biao">
    {{ data.region_name }}
  </div>
  <iframe :src="vr" frameborder="0" class="map"></iframe>
  <div class="left" v-show="!$store.state.show.zhan">
    <div class="one">
      <div class="title">
        <span>小区概况</span>
      </div>
      <div class="one_dis">
        <community :dataSex="data.dataSex" :dataFace="data.dataFace" :dataType="data.dataType" :dataHuJi="data.dataHuJi" :householder="data.householder" :user_sum="data.user_sum" :unit="data.unit" :building="data.building" @dialogHu="showDialogHu" @dialog2="showDialog2" @dialogDong="showDialogDong" @dialogHuJi="showDialogHuJi" @dialogRenYuan="showDialogRenYuan"> </community>
      </div>
    </div>
  </div>
  <div class="right" v-show="!$store.state.show.zhan">
    <div class="one">
      <div class="title"><span>年龄结构</span></div>
      <div class="one_dis">
        <ageConstruction :dataAge="data.dataAge" :subObj="data.subObj" @dialogAge="showDialogAge"></ageConstruction>
      </div>
    </div>
    <div class="two">
      <div class="title"><span>人员类别</span></div>
      <div class="two_dis">
        <personType :dataStudent="data.dataStudent" :dataSolier="data.dataSolier" :dataPerson="data.dataPerson" :dataImportant="data.dataImportant" :dataSpecial="data.dataSpecial" @dialog="showDialog" @dialogStudent="showDialogStudent" @dialogSolier="showDialogSolier"></personType>
      </div>
    </div>
  </div>
  <div class="bottom" v-show="!$store.state.show.zhan">
    <div class="title1"><span>民意互动情况统计</span></div>
    <div class="bot_dis">
      <interative :dataInteraction="data.dataInteraction"></interative>
    </div>
  </div>
  <div class="mengban" v-show="$store.state.community.isClosed"></div>
  <div class="dialog" v-show="$store.state.community.isClosed1">
    <tableVue @close="closeDialog" v-if="$store.state.community.no === 1" :type="'3'" :code="$route.query.code"></tableVue>
    <tableVue2 @close="closeDialog" v-if="$store.state.community.no === 2" :type="'3'" :code="$route.query.code"></tableVue2>
    <tableVue3 @close="closeDialog" v-if="$store.state.community.no === 3" :type="'3'" :code="$route.query.code"></tableVue3>
  </div>
  <!-- 年龄构成  -->
  <div class="dialog" v-if="$store.state.community.isClosedAge">
    <allAge @closeAge="closeDialogAge" v-if="$store.state.community.updateData" :focusAge="data.focusAge || $store.state.community.age"></allAge>
  </div>
  <!-- 总户数 -->
  <div class="dialog" v-if="$store.state.community.isClosedHu">
    <allHouseHolds @closeHu="closeDialogHu"></allHouseHolds>
  </div>
  <!-- 总小区数 -->
  <!-- <div class="dialog" v-show="isClosedDong">
    <allDong @closeDong="closeDialogDong"></allDong>
  </div> -->
  <!-- 总人数 -->
  <div class="dialog" v-if="$store.state.community.isClosed2">
    <allPeople @close2="closeDialog2"></allPeople>
  </div>
  <!-- 户籍情况 -->
  <div class="dialog" v-if="$store.state.community.isClosedHuJi">
    <huJi @closeHuJi="closeDialogHuJi"></huJi>
  </div>
  <!-- 人员情况 -->
  <div class="dialog" v-if="$store.state.community.isClosedRenYuan">
    <renYuan @closeRenYuan="closeDialogRenYuan"></renYuan>
  </div>
  <!-- 学生 -->
  <div class="dialog" v-if="$store.state.community.isClosedStudent">
    {{ data.focusStudent + '-----' +  $store.state.community.student + '----' + data.focusStudent || $store.state.community.student}}
    <student @closeStudent="closeDialogStudent" :focusStudent="$store.state.community.student" v-if="$store.state.community.updateStudent"></student>
  </div>
  <!-- 重点监管人群 -->
  <div class="dialog" v-if="$store.state.community.isClosedSolier">
    <solier @closeSolier="closeDialogSolier" :focusSolier="$store.state.community.solier" v-if="$store.state.community.updateSolier"></solier>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus'
import { statisticalData } from '../api'
import tableVue from '../components/table.vue'
import tableVue2 from '../components/table2.vue'
import tableVue3 from '../components/table3.vue'
import allAge from '../components/allAge.vue'
import allHouseHolds from '../components/allHouseHolds.vue'
import allDong from '../components/grid/allDong.vue'
import allBranch from '../components/allBranch.vue'
import allPeople from '../components/grid/allPeople.vue'
import huJi from '../components/grid/huJi.vue'
import renYuan from '../components/grid/renYuan.vue'
import student from '../components/grid/student.vue'
import solier from '../components/grid/solier.vue'

import interative from '../components/interative.vue'
import personType from '../components/grid/personType.vue'
import community from '../components/grid/community.vue'
import ageConstruction from '../components/grid/ageConstruction.vue'
import bussinessCase from '../components/bussinessCase.vue'
import { onMounted, onUnmounted, ref, reactive } from 'vue'
import { useStore } from 'vuex' // 引入useStore 方法
import { useRoute } from 'vue-router'

const route = useRoute()
const store = useStore() // 该方法用于返回store 实例
let name = ref('')
const no = ref(1)
const vr = ref('')
const isClosed = ref(false)
const isClosedAge = ref(false)
let updateData = ref(false)
let updateStudent = ref(false)
let updateSolier = ref(false)
const isClosed1 = ref(false)
const isClosed2 = ref(false)
const isClosedHu = ref(false)
const isClosedDong = ref(false)
const isClosedHuJi = ref(false)
const isClosedRenYuan = ref(false)
const isClosedSolier = ref(false)
const isClosedStudent = ref(false)
const data = reactive({
  region_name: '',
  householder: 0,
  building: 0,
  unit: 0,
  user_sum: 0,
  subObj: {},
  dataAge: [],
  list: [
    {
      value1: 340,
      value2: '户数（户）',
    },
    {
      value1: 10,
      value2: '栋数（栋）',
    },
    {
      value1: 10,
      value2: '单元（个）',
    },
    {
      value1: 5623,
      value2: '人数（人）',
    },
  ],
  dataSex: [],
  dataFace: [],
  dataType: [],
  dataHuJi: [],
  dataStudent: [
    {
      name: '大学',
      value: 200,
    },
    {
      name: '高中',
      value: 150,
    },
    {
      name: '初中',
      value: 200,
    },
    {
      name: '小学',
      value: 100,
    },
  ],
  dataSolier: [],
  dataPerson: [],
  dataSpecial: [],
  dataImportant: [],
  dataInteraction: {
    data1: [10, 5, 8, 2, 20, 5, 10, 6],
    data2: [20, 10, 5, 3, 10, 5, 20, 6],
    data3: [4, 20, 6, 10, 5, 20, 6, 10],
    data4: [5, 10, 6, 20, 2, 10, 5, 6],
  },
  focusAge: null,
  focusStudent: null,
  focusSolier: null,
})
onMounted(() => {
  name.value = route.query.code
  if (name.value) {
    getDeatil(name.value)
  }
})
async function getDeatil(val) {
  const res = await statisticalData({
    type: '3',
    code: val,
  })
  if (res.code == 200) {
    data.region_name = res.data.region_name
    data.householder = res.data.householder
    data.user_sum = res.data.user_sum
    data.building = res.data.building
    data.unit = res.data.unit
    data.dataHuJi = res.data.domicile_from
    data.dataType = res.data.domicile_type
    data.dataSex = res.data.gender
    data.dataFace = res.data.political
    data.dataAge = res.data.age
    data.dataPerson = res.data.famous_type
    data.dataSpecial = res.data.special_group
    data.dataImportant = res.data.focus_type
    data.dataSolier = res.data.soldier_type
    data.dataStudent = res.data.student
    data.subObj = {
      teenager: res.data.teenager,
      teenager_percent: res.data.teenager_percent,
      middle_aged: res.data.middle_aged,
      middle_aged_percent: res.data.middle_aged_percent,
      old_aged: res.data.old_aged,
      old_aged_percent: res.data.old_aged_percent,
    }
    vr.value = res.data.vr_url
  } else {
    ElMessage.error(res.message)
  }
  // console.log(data.dataFrom);
}
function showAll() {
  store.dispatch('changeZhan')
}
function showDialog(val) {
  no.value = val
  // isClosed.value = true
  // isClosed1.value = true
  store.dispatch('changeCommunityMask', true)
  store.dispatch('changeCommunityClosed1', true)
}
function closeDialog() {
  // isClosed.value = false
  // isClosed1.value = false
  store.dispatch('changeCommunityMask', false)
  store.dispatch('changeCommunityClosed1', false)
}

//  年龄构成
function showDialogAge(v) {
  data.focusAge = v
  // updateData.value = true
  // isClosed.value = true
  // isClosedAge.value = true

  store.dispatch('changeCommunityMask', true)
  store.dispatch('changeCommunityClosedAge', true)
  store.dispatch('changeCommunityUpdateData', true)
}
function closeDialogAge() {
  // updateData.value = false
  // isClosed.value = false
  // isClosedAge.value = false
  store.dispatch('changeCommunityMask', false)
  store.dispatch('changeCommunityClosedAge', false)
  store.dispatch('changeCommunityUpdateData', false)
}
// 总户数
function showDialogHu() {
  // isClosed.value = true
  // isClosedHu.value = true
  store.dispatch('changeCommunityMask', true)
  store.dispatch('changeCommunityClosedHu', true)
}
function closeDialogHu() {
  // isClosed.value = false
  // isClosedHu.value = false
  store.dispatch('changeCommunityMask', false)
  store.dispatch('changeCommunityClosedHu', false)
}
// 总小区数
function showDialogDong() {
  isClosed.value = true
  isClosedDong.value = true
}
function closeDialogDong() {
  isClosed.value = false
  isClosedDong.value = false
}

// 总人数
function showDialog2() {
  // isClosed.value = true
  // isClosed2.value = true
  store.dispatch('changeCommunityMask', true)
  store.dispatch('changeCommunityClosed2', true)
}
function closeDialog2() {
  // isClosed.value = false
  // isClosed2.value = false
  store.dispatch('changeCommunityMask', false)
  store.dispatch('changeCommunityClosed2', false)
}
// 户籍情况
function showDialogHuJi() {
  // isClosed.value = true
  // isClosedHuJi.value = true
  store.dispatch('changeCommunityMask', true)
  store.dispatch('changeCommunityClosedHuJi', true)
}
function closeDialogHuJi() {
  // isClosed.value = false
  // isClosedHuJi.value = false
  store.dispatch('changeCommunityMask', false)
  store.dispatch('changeCommunityClosedHuJi', false)
}
// 人员情况
function showDialogRenYuan() {
  // isClosed.value = true
  // isClosedRenYuan.value = true
  store.dispatch('changeCommunityMask', true)
  store.dispatch('changeCommunityClosedRenYuan', true)
}
function closeDialogRenYuan() {
  // isClosed.value = false
  // isClosedRenYuan.value = false
  store.dispatch('changeCommunityMask', false)
  store.dispatch('changeCommunityClosedRenYuan', false)
}
// 学生
function showDialogStudent(v) {
  // isClosed.value = true
  // isClosedStudent.value = true
  // updateStudent.value = true
  store.dispatch('changeCommunityMask', true)
  store.dispatch('changeCommunityClosedStudent', true)
  store.dispatch('changeCommunityUpdateStudent', true)
  focusStudent.value = v
}
function closeDialogStudent() {
  // isClosed.value = false
  // isClosedStudent.value = false
  // data.updateStudent = false
  store.dispatch('changeCommunityMask', false)
  store.dispatch('changeCommunityClosedStudent', false)
  store.dispatch('changeCommunityUpdateStudent', false)
}
// 军人
function showDialogSolier(v) {
  // isClosed.value = true
  // isClosedSolier.value = true
  // updateSolier.value = true
  store.dispatch('changeCommunityMask', true)
  store.dispatch('changeCommunityClosedSolier', true)
  store.dispatch('changeCommunityUpdateSolier', true)
  data.focusSolier = v
}
function closeDialogSolier() {
  // isClosed.value = false
  // isClosedSolier.value = false
  // updateSolier.value = false
  store.dispatch('changeCommunityMask', false)
  store.dispatch('changeCommunityClosedSolier', false)
  store.dispatch('changeCommunityUpdateSolier', false)
}
onUnmounted(() => {})
</script>
<style scoped lang="scss">
.goback {
  width: 60px;
  height: 30px;
  background: url(/di01.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  position: absolute;
  left: 240px;
  top: 37px;
  color: white;
  text-align: center;
  line-height: 30px;
  cursor: pointer;

  .backText {
    vertical-align: middle;
    font-size: 16px;
  }
}

.qiu {
  z-index: 2;
  position: absolute;
  right: 250px;
  top: 40px;
  cursor: pointer;
}

#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.label {
  width: 900px;
  height: 200px;
  font-size: 40px;
  color: red;
  position: absolute;
  z-index: 1;
}

.left {
  z-index: 1;
  width: 500px;
  height: 93%;
  position: absolute;
  left: 0;
  top: 113px;

  .one {
    height: 967px;

    .one_dis {
      width: 100%;
      height: 837px;
    }
  }
}

.right {
  z-index: 1;
  width: 500px;
  height: 93%;
  position: absolute;
  right: 0;
  top: 113px;

  .one {
    height: 225px;

    .one_dis {
      height: 175px;
    }
  }

  .two {
    height: 483px;

    .two_dis {
      height: 433px;
    }
  }

  .three {
    height: 260px;

    .three_dis {
      height: 210px;
      position: relative;
      right: 2%;
    }
  }
}

.bottom {
  width: 880px;
  height: 259px;
  position: absolute;
  z-index: 2;
  bottom: 0px;
  left: 500px;
}

.title1 {
  width: 880px;
  height: 50px;
  background: url(/biaoti_di1.png) no-repeat;
  background-size: contain;
  margin: 0 20px;

  span {
    font-size: 22px;
    padding-left: 40px;
    font-weight: 600;
    line-height: 50px;
    color: #fff;
    letter-spacing: 5px;
  }
}

.biao {
  width: 229px;
  height: 55px;
  position: absolute;
  left: 856px;
  top: 107px;
  background: url(/di04.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  color: white;
  font-size: 20px;
  text-align: center;
  line-height: 55px;
}

.title {
  height: 50px;
  background: url('/biaoti_di.png') no-repeat;
  background-size: cover;
  margin: 0 20px;

  span {
    font-size: 22px;
    padding-left: 40px;
    font-weight: 600;
    line-height: 50px;
    color: #fff;
    letter-spacing: 5px;
  }
}

.map {
  width: 100%;
  height: 100%;
}

.mengban {
  width: 1920px;
  height: 1080px;
  background-color: black;
  left: 0;
  opacity: 0.7;
  position: absolute;
  top: 0;
  z-index: 3;
  filter: alpha(opacity=70);
}

.dialog {
  z-index: 4;
  width: 1300px;
  height: 740px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid #0c3f7e;
}
</style>
