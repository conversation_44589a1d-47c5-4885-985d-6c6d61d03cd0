<template>
  <div class="goback" @click="$router.replace('/grid')" style="font-size: 12px">
    <span class="backText">&emsp;返回</span>
  </div>
  <div class="qiu" @click="showAll">
    <img src="/zhankai.png" v-show="$store.state.show.zhan" alt="" />
    <img src="/shousuo.png" v-show="!$store.state.show.zhan" alt="" />
  </div>
  <div class="biao">
    {{ $route.query.code == 10 ? '望凤街网格' : '中兴街网格' }}
  </div>
  <iframe :src="mapA" frameborder="0" class="mapa"></iframe>
  <div class="left" v-if="!$store.state.show.zhan">
    <div class="one">
      <div class="title">
        <span>网格概况</span>
      </div>
      <div class="one_dis">
        <grid-content :householder="data.householder" :user_sum="data.user_sum" :gridList="data.list" :dataHuJi="data.dataHuJi" :dataSex="data.dataSex" :dataFace="data.dataFace" :dataType="data.dataType" :xiaoqu="data.xiaoqu" @dialogHu="showDialogHu" @dialogCommunity="showDialogCommunity" @dialogBranch="showDialogBranch" @dialog2="showDialog2" @dialogHuJi="showDialogHuJi" @dialogRenYuan="showDialogRenYuan"> </grid-content>
      </div>
    </div>
    <div class="two">
      <div class="title"><span>经济结构</span></div>
      <div class="two_dis">
        <bussinessCase @dialogBussiness="showDialog"></bussinessCase>
      </div>
    </div>
  </div>
  <div class="right" v-if="!$store.state.show.zhan">
    <div class="one">
      <div class="title"><span>年龄结构</span></div>
      <div class="one_dis">
        <ageConstruction :dataAge="data.dataAge" :subObj="data.subObj" @dialogAge="showDialogAge"></ageConstruction>
      </div>
    </div>
    <div class="two">
      <div class="title"><span>人员类别</span></div>
      <div class="two_dis">
        <personType :dataStudent="data.dataStudent" :dataSolier="data.dataSolier" :dataPerson="data.dataPerson" :dataImportant="data.dataImportant" :dataSpecial="data.dataSpecial" @dialog="showDialog" @dialogStudent="showDialogStudent" @dialogSolier="showDialogSolier"></personType>
      </div>
    </div>
  </div>
  <div class="bottom" v-if="!$store.state.show.zhan">
    <div class="title1"><span>民意互动情况统计</span></div>
    <div class="bot_dis">
      <interative :dataInteraction="data.dataInteraction"></interative>
    </div>
  </div>
  <div class="mengban" v-show="$store.state.grid.isClosed"></div>
  <div class="dialog" v-show="$store.state.grid.isClosed1">
    <tableVue @close="closeDialog" v-if="$store.state.grid.no === 1" :type="'2'" :code="$route.query.code"></tableVue>
    <tableVue2 @close="closeDialog" v-if="$store.state.grid.no === 2" :type="'2'" :code="$route.query.code"></tableVue2>
    <tableVue3 @close="closeDialog" v-if="$store.state.grid.no === 3" :type="'2'" :code="$route.query.code"></tableVue3>
    <!-- 经济结构 事业单位 -->
    <tableVue4 @close="closeDialog" v-if="$store.state.grid.no === 4" :type="'1'" :code="''"></tableVue4>
  </div>
  <!-- 年龄构成  -->
  <div class="dialog" v-if="$store.state.grid.isClosedAge">
    <allAge @closeAge="closeDialogAge" v-if="$store.state.grid.updateData" :focusAge="data.focusAge || $store.state.grid.age"></allAge>
  </div>
  <!-- 总户数 -->
  <div class="dialog" v-if="$store.state.grid.isClosedHu">
    <allHouseHolds @closeHu="closeDialogHu"></allHouseHolds>
  </div>
  <!-- 总小区数 -->
  <div class="dialog" v-show="$store.state.grid.isClosedCommunity">
    <allCommunity @closeCommunity="closeDialogCommunity"></allCommunity>
  </div>
  <!-- 党支部 -->
  <div class="dialog" v-show="$store.state.grid.isClosedBranch">
    <allBranch @closeBranch="closeDialogBranch"></allBranch>
  </div>
  <!-- 总人数 -->
  <div class="dialog" v-if="$store.state.grid.isClosed2">
    <allPeople @close2="closeDialog2"></allPeople>
  </div>
  <!-- 户籍情况 -->
  <div class="dialog" v-if="$store.state.grid.isClosedHuJi">
    <huJi @closeHuJi="closeDialogHuJi"></huJi>
  </div>
  <!-- 人员情况 -->
  <div class="dialog" v-if="$store.state.grid.isClosedRenYuan">
    <renYuan @closeRenYuan="closeDialogRenYuan"></renYuan>
  </div>
  <!-- 学生 -->
  <div class="dialog" v-if="$store.state.grid.isClosedStudent">
    <student @closeStudent="closeDialogStudent" :focusStudent="data.focusStudent || $store.state.grid.student" v-if="$store.state.grid.updateStudent"></student>
  </div>
  <!-- 重点监管人群 -->
  <div class="dialog" v-if="$store.state.grid.isClosedSolier">
    <solier @closeSolier="closeDialogSolier" :focusSolier="data.focusSolier || $store.state.grid.solier" v-if="$store.state.grid.updateSolier"></solier>
  </div>
</template>
<script setup>
import { statisticalData, getHousehold } from '../api'
import { ElMessage } from 'element-plus'
import tableVue from '../components/table.vue'
import tableVue2 from '../components/table2.vue'
import tableVue3 from '../components/table3.vue'
import tableVue4 from '../components/table4.vue'
import allAge from '../components/allAge.vue'
import allHouseHolds from '../components/allHouseHolds.vue'
import allCommunity from '../components/allCommunity.vue'
import allBranch from '../components/allBranch.vue'
import allPeople from '../components/grid/allPeople.vue'
import huJi from '../components/grid/huJi.vue'
import renYuan from '../components/grid/renYuan.vue'
import student from '../components/grid/student.vue'
import solier from '../components/grid/solier.vue'

import interative from '../components/interative.vue'
import personType from '../components/grid/personType.vue'
import ageConstruction from '../components/grid/ageConstruction.vue'
import bussinessCase from '../components/bussinessCase.vue'
import gridContent from '../components/grid/gridContent.vue'
import { onMounted, onUnmounted, ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex' // 引入useStore 方法
const store = useStore() // 该方法用于返回store 实例
const router = useRouter()
const route = useRoute()
const mapA = ref('')
const suoA = ref(true)
const suoB = ref(true)
const isClosed = ref(false)
const no = ref(1)
const isClosedAge = ref(false)
const isClosed1 = ref(false)
const isClosed2 = ref(false)
const isClosedHu = ref(false)
const isClosedCommunity = ref(false)
const isClosedBranch = ref(false)
const isClosedHuJi = ref(false)
const isClosedRenYuan = ref(false)
const isClosedSolier = ref(false)
const isClosedStudent = ref(false)

let updateData = ref(false)
let updateStudent = ref(false)
let updateSolier = ref(false)
let data = reactive({
  xiaoqu: 0,
  subObj: {},
  householder: 0,
  user_sum: 0,
  dataHuJi: [],
  dataType: [],
  dataSex: [],
  dataFace: [],
  bussiness1: 2,
  bussiness2: 1,
  dataAge: [
    {
      name: '0-3',
      value: 50,
    },
    {
      name: '3-6',
      value: 100,
    },
    {
      name: '6-12',
      value: 200,
    },
    {
      name: '12-18',
      value: 400,
    },
    {
      name: '18-59',
      value: 1000,
    },
    {
      name: '59-79',
      value: 500,
    },
    {
      name: '80以上',
      value: 200,
    },
  ],
  dataStudent: [
    {
      name: '大学',
      value: 200,
    },
    {
      name: '高中',
      value: 150,
    },
    {
      name: '初中',
      value: 200,
    },
    {
      name: '小学',
      value: 100,
    },
  ],
  dataSolier: [
    {
      name: '优抚对象',
      value: 20,
    },
    {
      name: '预备役',
      value: 12,
    },
    {
      name: '退役',
      value: 10,
    },
    {
      name: '现役',
      value: 5,
    },
  ],
  dataPerson: [
    { value: 30, name: '致富带头人' },
    { value: 28, name: '文化名流' },
    { value: 26, name: '职业能人' },
    { value: 24, name: '其他' },
    // { value: 22, name: '职业能人5' },
    // { value: 20, name: '职业能人6' },
    // { value: 18, name: '职业能人7' },
    // { value: 16, name: '职业能人8' }
  ],
  dataSpecial: [
    {
      value: 3,
      name: '低保户',
    },
    {
      value: 5,
      name: '失独家庭',
    },
    {
      value: 9,
      name: '困境儿童',
    },
    {
      value: 17,
      name: '孤寡老人',
    },
    {
      value: 5,
      name: '残疾人',
    },
    {
      value: 7,
      name: '其他',
    },
  ],
  dataImportant: [
    {
      value: 5,
      name: '社区矫正',
    },
    {
      value: 5,
      name: '刑满释放',
    },
    {
      value: 25,
      name: '精神病患者',
    },
    {
      value: 1,
      name: '吸毒人员',
    },
    {
      value: 5,
      name: '其他',
    },
  ],
  dataInteraction: {
    data1: [10, 5, 8, 2, 20, 5, 10, 6],
    data2: [20, 10, 5, 3, 10, 5, 20, 6],
    data3: [4, 20, 6, 10, 5, 20, 6, 10],
    data4: [5, 10, 6, 20, 2, 10, 5, 6],
  },
  focusAge: 1,
  focusStudent: 1,
  focusSolier: 1,
})
const n = ref(0)
onMounted(() => {
  window.addEventListener('message', fun)
  n.value = route.query.code
  if (n.value == 20) {
    // 中兴街
    mapA.value = '/mapB'
    getDeatil('20')
  } else {
    // 望凤街
    mapA.value = '/mapA'
    getDeatil('10')
  }
  getHousehold({ limit: 15, offset: 0, type: '2', code: route.query.code, householder_name: '' }).then((res) => {
    data.householder = res.data.count
  })
})
async function getDeatil(val) {
  const res = await statisticalData({
    type: '2',
    code: val,
  })
  if (res.code == 200) {
    // data.householder = res.data.householder
    data.user_sum = res.data.user_sum
    data.dataHuJi = res.data.domicile_from
    data.dataType = res.data.domicile_type
    data.dataSex = res.data.gender
    data.dataFace = res.data.political
    data.dataAge = res.data.age
    data.dataPerson = res.data.famous_type
    data.dataSpecial = res.data.special_group
    data.dataImportant = res.data.focus_type
    data.dataSolier = res.data.soldier_type
    data.dataStudent = res.data.student
    data.xiaoqu = res.data.xiaoqu
    data.subObj = {
      teenager: res.data.teenager,
      teenager_percent: res.data.teenager_percent,
      middle_aged: res.data.middle_aged,
      middle_aged_percent: res.data.middle_aged_percent,
      old_aged: res.data.old_aged,
      old_aged_percent: res.data.old_aged_percent,
    }
  } else {
    ElMessage.error(res.message)
  }
  // console.log(data.dataFrom);
}
function fun(event) {
  let type = event.data.type
  if (type == 'a' && suoA.value) {
    suoA.value = false
    router.push('/specificCommunity?code=' + event.data.value)
  }

  if (type == 'b' && suoB.value) {
    suoB.value = false
    router.push('/specificCommunity?code=' + event.data.value)
  }
}
function showAll() {
  store.dispatch('changeZhan')
}
// 控制弹窗
function showDialog(val) {
  no.value = val
  
  // isClosed.value = true
  // isClosed1.value = true
  store.dispatch('changeGridNo', val)
  store.dispatch('changeGridMask', true)
  store.dispatch('changeGridClosed1', true)
}
function closeDialog() {
  // isClosed.value = false
  // isClosed1.value = false
  store.dispatch('changeGridMask', false)
  store.dispatch('changeGridClosed1', false)
}

//  年龄构成
function showDialogAge(v) {
  // updateData.value = true
  data.focusAge = v
  // isClosed.value = true
  // isClosedAge.value = true
  store.dispatch('changeGridMask', true)
  store.dispatch('changeGridClosedAge', true)
  store.dispatch('changeGridUpdateData', true)
}
function closeDialogAge() {
  // updateData.value = false
  // isClosed.value = false
  // isClosedAge.value = false
  store.dispatch('changeGridMask', false)
  store.dispatch('changeGridClosedAge', false)
  store.dispatch('changeGridUpdateData', false)
}
// 总户数
function showDialogHu() {
  // isClosed.value = true
  // isClosedHu.value = true
  store.dispatch('changeGridMask', true)
  store.dispatch('changeGridClosedHu', true)
}
function closeDialogHu() {
  // isClosed.value = false
  // isClosedHu.value = false
  store.dispatch('changeGridMask', false)
  store.dispatch('changeGridClosedHu', false)
}
// 总小区数
function showDialogCommunity() {
  // isClosed.value = true
  // isClosedCommunity.value = true
  store.dispatch('changeGridMask', true)
  store.dispatch('changeGridClosedCommunity', true)
}
function closeDialogCommunity() {
  // isClosed.value = false
  // isClosedCommunity.value = false
  store.dispatch('changeGridMask', false)
  store.dispatch('changeGridClosedCommunity', false)
}
// 党支部
function showDialogBranch() {
  // isClosed.value = true
  // isClosedBranch.value = true
  store.dispatch('changeGridMask', true)
  store.dispatch('changeGridClosedBranch', true)
}
function closeDialogBranch() {
  // isClosed.value = false
  // isClosedBranch.value = false
  store.dispatch('changeGridMask', false)
  store.dispatch('changeGridClosedBranch', false)
}
// 总人数
function showDialog2() {
  // isClosed.value = true
  // isClosed2.value = true
  store.dispatch('changeGridMask', true)
  store.dispatch('changeGridClosed2', true)
}
function closeDialog2() {
  // isClosed.value = false
  // isClosed2.value = false
  store.dispatch('changeGridMask', false)
  store.dispatch('changeGridClosed2', false)
}
// 户籍情况
function showDialogHuJi() {
  // isClosed.value = true
  // isClosedHuJi.value = true
  store.dispatch('changeGridMask', true)
  store.dispatch('changeGridClosedHuJi', true)
}
function closeDialogHuJi() {
  // isClosed.value = false
  // isClosedHuJi.value = false
  store.dispatch('changeGridMask', false)
  store.dispatch('changeGridClosedHuJi', false)

}
// 人员情况
function showDialogRenYuan() {
  // isClosed.value = true
  // isClosedRenYuan.value = true
  store.dispatch('changeGridMask', true)
  store.dispatch('changeGridClosedRenYuan', true)
}
function closeDialogRenYuan() {
  // isClosed.value = false
  // isClosedRenYuan.value = false
  store.dispatch('changeGridMask', false)
  store.dispatch('changeGridClosedRenYuan', false)
}
// 学生
function showDialogStudent(v) {
  // updateStudent.value = true
  data.focusStudent = v
  // isClosed.value = true
  // isClosedStudent.value = true
  store.dispatch('changeGridMask', true)
  store.dispatch('changeGridClosedStudent', true)
  store.dispatch('changeGridUpdateStudent', true)
}
function closeDialogStudent() {
  // isClosed.value = false
  // isClosedStudent.value = false
  // updateStudent.value = false
  store.dispatch('changeGridMask', false)
  store.dispatch('changeGridClosedStudent', false)
  store.dispatch('changeGridUpdateStudent', false)
}
// 军人
function showDialogSolier(v) {
  data.focusSolier = v
  // updateSolier.value = true
  // isClosed.value = true
  // isClosedSolier.value = true
  store.dispatch('changeGridMask', true)
  store.dispatch('changeGridClosedSolier', true)
  store.dispatch('changeGridUpdateSolier', true)
}
function closeDialogSolier() {
  // isClosed.value = false
  // isClosedSolier.value = false
  // updateSolier.value = false
  store.dispatch('changeGridMask', false)
  store.dispatch('changeGridClosedSolier', false)
  store.dispatch('changeGridUpdateSolier', false)
}
onUnmounted(() => {
  mapA.value = mapA.value + '?' + new Date().getTime()
  window.removeEventListener('message', fun)
})
</script>
<style scoped lang="scss">
#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.left {
  z-index: 1;
  width: 500px;
  height: 967px;
  position: absolute;
  top: 113px;

  .one {
    width: 100%;
    height: 737px;

    .one_dis {
      width: 100%;
      height: 687px;
    }
  }

  .two {
    height: 343px;

    .two_dis {
      height: 293px;
    }
  }
}

.right {
  z-index: 1;
  width: 500px;
  height: 967px;
  position: absolute;
  right: 0;
  top: 113px;

  .one {
    height: 225px;

    .one_dis {
      height: 175px;
    }
  }

  .two {
    height: 483px;

    .two_dis {
      height: 433px;
    }
  }

  .three {
    height: 260px;

    .three_dis {
      height: 210px;
      position: relative;
      right: 2%;
    }
  }
}

.title {
  height: 50px;
  background: url(/biaoti_di.png) no-repeat;
  background-size: 100% 100%;
  margin: 0 20px;

  span {
    font-size: 22px;
    padding-left: 40px;
    font-weight: 600;
    line-height: 50px;
    color: #fff;
    letter-spacing: 5px;
  }
}

.goback {
  width: 60px;
  height: 30px;
  background: url(/di01.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  position: absolute;
  left: 240px;
  top: 37px;
  color: white;
  text-align: center;
  line-height: 30px;
  cursor: pointer;

  .backText {
    vertical-align: middle;
    font-size: 16px;
  }
}

.qiu {
  z-index: 2;
  position: absolute;
  right: 250px;
  top: 40px;
  cursor: pointer;

  img {
    width: 41px;
    height: 37px;
  }
}

.biao {
  width: 229px;
  height: 55px;
  position: absolute;
  left: 856px;
  top: 107px;
  background: url(/di04.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  color: white;
  font-size: 20px;
  text-align: center;
  line-height: 55px;
  letter-spacing: 3px;
}

.mapa {
  width: 100%;
  height: 100%;
}

.mengban {
  width: 1920px;
  height: 1080px;
  background-color: black;
  left: 0;
  opacity: 0.7;
  position: absolute;
  top: 0;
  z-index: 3;
  filter: alpha(opacity=70);
}

.dialog {
  z-index: 4;
  width: 1300px;
  height: 740px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid #0c3f7e;
}

.bottom {
  width: 880px;
  height: 259px;
  position: absolute;
  z-index: 2;
  bottom: 0px;
  left: 500px;
}

.title1 {
  width: 880px;
  height: 50px;
  background: url(/biaoti_di1.png) no-repeat;
  background-size: contain;
  margin: 0 20px;

  span {
    font-size: 22px;
    padding-left: 40px;
    font-weight: 600;
    line-height: 50px;
    color: #fff;
    letter-spacing: 5px;
  }
}
</style>
