
<template>
  <!-- 没有引用该文件 -->
  <!-- <div class="goback" @click="$router.replace('/wangge')" style="font-size:12px">
    <span class="backText">&emsp;返回</span>
  </div>
  <div class="qiu" @click="showAll">
    <img src="/zhankai.png" v-if="$store.state.show.zhan" alt="">
    <img src="/shousuo.png" v-else alt="">
  </div>
  <div class="biao">
    中兴街网格
  </div>
  <iframe :src=mapB frameborder="0" class="mapb"></iframe>
  <div id="cesiumContainer"></div>
  <div class="left" v-show="!$store.state.show.zhan">
    <div class="one">
      <div class="title">
        <span>社区基础数据</span>
      </div>
      <div class="one_dis">
        <span class="dis_" v-for="(item, index) in data.list" :key="index">
          <span class="top">{{ item.value1 }}</span>
          <img src="/public/di02.png" alt="">
          <span class="bottom">{{ item.value2 }}</span>
        </span>
      </div>
    </div>
    <div class="two">
      <div class="title"><span>人口数据情况</span></div>
      <div class="two_dis"></div>
    </div>
    <div class="three">
      <div class="title"><span>年龄结构情况</span></div>
      <div class="three_dis"></div>
    </div>
  </div>
  <div class="right" v-show="!$store.state.show.zhan">
    <div class="one">
      <div class="title"><span>社区经营情况</span></div>
      <div class="one_dis"></div>
    </div>
    <div class="two">
      <div class="title"><span>特殊人员情况</span></div>
      <div class="two_dis"></div>
    </div>
  </div> -->
</template>
<script setup>

import * as Cesium from 'cesium';
import { onMounted, onUnmounted, ref, reactive } from 'vue';
import { useRouter } from 'vue-router'
import { useStore } from 'vuex' // 引入useStore 方法
const store = useStore()  // 该方法用于返回store 实例
const router = useRouter()
const mapB = ref('/mapB')
const suoB = ref(true)
let data = reactive({
  data: [
    {
      lng:
        105.81203,
      lat: 32.444008,
      infoId: 1,
      text: '阳光水岸',
    },
    {
      lng: 105.812323,
      lat:
        32.44498,
      infoId: 2,
      text: '刑警队小区',
    },
    {
      lng: 105.812434,
      lat:
        32.446217,
      infoId: 3,
      text: '工行一二号园',
    },
    {
      lng:
        105.81219,
      lat:
        32.446949,
      infoId: 4,
      text: '工行五六号园',
    },
    {
      lng:
        105.811517,
      lat:
        32.447217,
      infoId: 4,
      text: '则南第一小区',
    },

  ],
  list: [
    {
      value1: 2,
      value2: '网格数(个)'
    },
    {
      value1: 34,
      value2: '小区(个)'
    },
    {
      value1: 2468,
      value2: '户数(户)'
    },
    {
      value1: 5623,
      value2: '人数(人)'
    }
  ]
})

onMounted(() => {
  window.addEventListener('message', fun)
})
function fun(event) {
  // console.log(event);
  let type = event.data.type
  if (type == 'b' && suoB.value) {
    suoB.value = false
    router.push({
      path: '/specificCommunity',
      query: { id: event.data.value, name: event.data.name }
    })
  }
}
function showAll() {
  store.dispatch('changeZhan')
}


onUnmounted(() => {
  mapB.value = '/mapB' + '?' + new Date().getTime()
  window.removeEventListener('message', fun)
})
</script>
<style scoped lang="scss">
#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.label1 {
  width: 120px;
  height: 30px;
  font-size: 20px;
  color: white;
  position: absolute;
  z-index: 1;
  background: linear-gradient(to top, #8558f9, #d66eee);
  border-radius: 5px;
  text-align: center;
  line-height: 30px;
  top: 50%;
  left: 40%;
  transform: translate(-50%, -50%);
}

.label2 {
  width: 120px;
  height: 30px;
  font-size: 20px;
  color: white;
  position: absolute;
  z-index: 1;
  background: linear-gradient(to top, #f34e01, #ffac00);
  border-radius: 5px;
  text-align: center;
  line-height: 30px;
  top: 50%;
  left: 60%;
  transform: translate(-50%, -50%);
}

.left {
  z-index: 1;
  width: 442px;
  height: 93%;
  position: absolute;
  left: 0;
  top: 113px;

  .one {
    height: 221px;

    .one_dis {
      width: 100%;
      display: flex;
      height: 185px;
      // justify-content: space-between;

      .dis_ {
        color: #fff;
        width: 25%;
        text-align: center;

        img {
          width: 112px;
          height: 81px;
        }

        .top {
          font-size: 34px;
          position: relative;
          top: 40px;
          font-family: DIN;
          font-weight: bold;
          color: #FFFFFF;
          line-height: 45px;
          background: linear-gradient(0deg, rgba(119, 244, 245, 0.99) 1.2939453125%);
        }

        .bottom {
          font-size: 20px;
        }
      }
    }
  }

  .two {
    height: 329px;
  }

  .three {
    height: 35%;
  }



  .two_dis {
    height: 293px;
  }

  .three_dis {
    height: 339px;
  }


}

.right {
  z-index: 1;
  width: 442px;
  height: 93%;
  position: absolute;
  right: 0;
  top: 113px;

  .one {
    height: 255px;
  }

  .two {
    height: 712px;
  }
}

.title {
  height: 50px;
  background: url(/biaoti_di.png) no-repeat;
  background-size: 100% 100%;

  span {
    font-size: 26px;
    padding-left: 40px;
    font-weight: 600;
    line-height: 50px;
    color: #fff;
  }
}

.goback {
  width: 60px;
  height: 30px;
  background: url(/di01.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  position: absolute;
  left: 240px;
  top: 37px;
  color: white;
  text-align: center;
  line-height: 30px;
  cursor: pointer;

  .backText {
    vertical-align: middle;
    font-size: 16px;
  }
}

.qiu {
  z-index: 2;
  position: absolute;
  right: 250px;
  top: 40px;
  cursor: pointer;

  img {
    width: 41px;
    height: 37px;
  }
}

.biao {
  width: 229px;
  height: 55px;
  position: absolute;
  left: 856px;
  top: 107px;
  background: url(/di04.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  color: white;
  font-size: 24px;
  text-align: center;
  line-height: 55px;
}

.mapb {
  width: 100%;
  height: 100%;
}
</style>
