import { createStore } from 'vuex'

const store = createStore({
  state: {
    show: {
      zhan: true,
      scale: 1,
    },
    sheQu:{
      isClosed:false,
      isClosed1:false,
      isClosed2:false,
      isClosed3:false,
      isClosedHu:false,
      isClosedCommunity:false,
      isClosedBranch:false,
      isClosedAge:false,
      updateData:false,
      age:0,
      no:0
    },
    grid: {
      isClosed:false,
      isClosed1:false,
      isClosed2:false,
      isClosedHuJi:false,
      isClosedHu:false,
      isClosedCommunity:false,
      isClosedBranch:false,
      isClosedAge:false,
      updateData:false,
      isClosedRenYuan:false,
      isClosedStudent:false,
      isClosedSolier:false,
      updateStudent:false,
      updateSolier:false,
      age:0,
      no:0,
      student: 0,
      solier: 0,
      wangFeng:[
        {
          infoId: 2026,
          text: '阳光水岸小区',
        },
        {
          infoId: 2025,
          text: '刑警队住宿楼',
        },
        {
          infoId: 2013,
          text: '工行小区',
        },
        {
          infoId: 2027,
          text: '樱花小区',
        },
        {
          infoId: 2021,
          text: '蔷薇小区',
        },
        {
          infoId: 2016,
          text: '皇佳公寓',
        },
        {
          infoId: 2015,
          text: '和顺嘉苑',
        },
        {
          infoId: 2029,
          text: '则天二小区',
        },
        {
          infoId: 2030,
          text: '则天一小区',
        },
        {
          infoId: 2024,
          text: '信用社住宿楼',
        },
        {
          infoId: 2017,
          text: '金矿新区',
        },
        {
          infoId: 2022,
          text: '嘉利水岸花园',
        },
        {
          infoId: 2023,
          text: '兴和万科花园',
        },
        {
          infoId: 2018,
          text: '金龙小区',
        },
        {
          infoId: 2019,
          text: '林业站小区',
        },
        {
          infoId: 2028,
          text: '雍江皇庭小区',
        },
        {
          infoId: 2032,
          text: '自来水公司住宿楼',
        },
        {
          infoId: 2031,
          text: '装备处住宿楼',
        },
        {
          infoId: 2012,
          text: '工商所住宿楼',
        },
        {
          infoId: 2020,
          text: '派出所住宿楼',
        },
        {
          infoId: 2014,
          text: '管委会住宿楼',
        },
        {
          infoId: 2010,
          text: '陈怀宽住宿楼',
        },
        {
          infoId: 2011,
          text: '二建公司住宿楼',
        },
      ],
      zhongXing:[
        {
          infoId: 1015,
          text: '西城国际小区',
        },
        {
          infoId: 1019,
          text: '则天新景小区',
        },
        {
          infoId: 1010,
          text: '供销社住宿楼',
        },
        {
          infoId: 1011,
          text: '海晟名苑小区',
        },
        {
          infoId: 1018,
          text: '邮政公寓小区',
        },
        {
          infoId: 1013,
          text: '农行小区',
        },
        {
          infoId: 1016,
          text: '凰城·西江月小区',
        },
        {
          infoId: 1012,
          text: '西岭居小区',
        },
        {
          infoId: 1017,
          text: '新世纪小区',
        },
        {
          infoId: 1014,
          text: '税务小区',
        },
    
      ]
    },
    community: {
      isClosed:false,
      isClosed1:false,
      isClosed2:false,
      isClosedHuJi:false,
      isClosedHu:false,
      isClosedAge:false,
      updateData:false,
      isClosedRenYuan:false,
      isClosedStudent:false,
      isClosedSolier:false,
      updateStudent:false,
      updateSolier:false,
      age:0,
      no:0,
      student: 0,
      solier: 0,
    }
  },
  mutations: {
    CHANGEZHAN(state) {
      state.show.zhan = !state.show.zhan
    },
    OPENZHAN(state) {
      state.show.zhan = false
    },
    CLOSEZHAN(state) {
      state.show.zhan = true
    },
    CHANGESCALE(state, payload) {
      state.show.scale = payload
    },
    // 社区层面
    CHANGEMARSK(state, payload) {
      state.sheQu.isClosed = payload
    },
    CHANGECLOSED1(state, payload) {
      state.sheQu.isClosed1 = payload
    },
    CHANGECLOSED2(state, payload) {
      state.sheQu.isClosed2 = payload
    },

    CHANGECLOSED3(state, payload) {
      state.sheQu.isClosed3 = payload
    },
    CHANGECLOSEDHU(state, payload) {
      state.sheQu.isClosedHu = payload
    },
    CHANGECLOSEDCOMMUNITY(state, payload) {
      state.sheQu.isClosedCommunity = payload
    },
    CHANGECLOSEDBRANCH(state, payload) {
      state.sheQu.isClosedBranch = payload
    },
    CHANGECLOSEDAGE(state, payload) {
      state.sheQu.isClosedAge = payload
    },
    CHANGEUPDATEDATA(state, payload) {
      state.sheQu.updateData = payload
    },
    CHANGEAGE(state, payload) {
      state.sheQu.age = payload
    },
    CHANGENO(state, payload) {
      state.sheQu.no = payload
    },
    // 网格层面
    CHANGEGRIDMASK(state, payload) {
      state.grid.isClosed = payload
    },
    CHANGEGRIDCLOSED1(state, payload) {
      state.grid.isClosed1 = payload
    },
    CHANGEGRIDCLOSED2(state, payload) {
      state.grid.isClosed2 = payload
    },
    CHANGEGRIDCLOSEDHUJI(state, payload) {
      state.grid.isClosedHuJi = payload
    },
     CHANGEGRIDCLOSEDHU(state, payload) {
      state.grid.isClosedHu = payload
    },
    CHANGEGRIDCLOSEDCOMMUNITY(state, payload) {
      state.grid.isClosedCommunity = payload
    },
    CHANGEGRIDCLOSEDBRANCH(state, payload) {
      state.grid.isClosedBranch = payload
    },
    CHANGEGRIDCLOSEDAGE(state, payload) {
      state.grid.isClosedAge = payload
    },
    CHANGEGRIDCLOSEDRENYUAN(state, payload) {
      state.grid.isClosedRenYuan = payload
    },
    CHANGEGRIDUPDATEDATA(state, payload) {
      state.grid.updateData = payload
    },
    CHANGEGRIDCLOSEDSTUDENT(state, payload) {
      state.grid.isClosedStudent = payload
    },
     CHANGEGRIDCLOSEDSOLIER(state, payload) {
      state.grid.isClosedSolier = payload
    },
    CHANGEGRIDUPDATESOLIER(state, payload) {
      state.grid.updateSolier = payload
    },
    CHANGEGRIDUPDATESTUDENT(state, payload) {
      state.grid.updateStudent = payload
    },
    CHANGEGRIDSTUDENT(state, payload) {
      state.grid.student = payload
    },
    CHANGEGRIDSOLIER(state, payload) {
      state.grid.solier = payload
    },
    CHANGEGRIDAGE(state, payload) {
      state.grid.age = payload
    },
    CHANGEGRIDNO(state, payload) {
      state.grid.no = payload
    },

    // 小区层面
    CHANGECOMMUNITYMASK(state, payload) {
      state.community.isClosed = payload
    },
    CHANGECOMMUNITYCLOSED1(state, payload) {
      state.community.isClosed1 = payload
    },
    CHANGECOMMUNITYCLOSED2(state, payload) {
      state.community.isClosed2 = payload
    },
    CHANGECOMMUNITYCLOSEDHUJI(state, payload) {
      state.community.isClosedHuJi = payload
    },
     CHANGECOMMUNITYCLOSEDHU(state, payload) {
      state.community.isClosedHu = payload
    },
    CHANGECOMMUNITYCLOSEDSOLIER(state, payload) {
      state.community.isClosedSolier = payload
    },
    CHANGECOMMUNITYCLOSEDSTUDENT(state, payload) {
      state.community.isClosedStudent = payload
    },
    CHANGECOMMUNITYCLOSEDAGE(state, payload) {
      state.community.isClosedAge = payload
    },
    CHANGECOMMUNITYUPDATEDATA(state, payload) {
      state.community.updateData = payload
    },
    CHANGECOMMUNITYAGE(state, payload) {
      state.community.age = payload
    },
    CHANGECOMMUNITYNO(state, payload) {
      state.community.no = payload
    },
    CHANGECOMMUNITYCLOSEDRENYUAN(state, payload) {
      state.community.isClosedRenYuan = payload
    },
    CHANGECOMMUNITYSTUDENT(state, payload) {
      console.log("CHANGECOMMUNITYSTUDENT mutation被调用，payload:", payload);
      state.community.student = payload
      console.log("CHANGECOMMUNITYSTUDENT mutation被调用，payload:", payload);
    },
    CHANGECOMMUNITYSOLIER(state, payload) {
      state.community.solier = payload
    },
    CHANGECOMMUNITYUPDATESTUDENT(state, payload) {
      state.community.updateStudent = payload
     
    },
    CHANGECOMMUNITYUPDATESOLIER(state, payload) {
      state.community.updateSolier = payload
    },


  },
  actions: {
    changeZhan({ commit }) {
      commit('CHANGEZHAN')
    },
    openZhan({ commit }) {
      commit('OPENZHAN')
    },
    closeZhan({ commit }) {
      commit('CLOSEZHAN')
    },
    changeScale({ commit }, payload) {
      commit('CHANGESCALE', payload)
    },
    // 社区层面
    changeMask({ commit }, payload) {
      commit('CHANGEMARSK', payload)
    },
    changeClosed1({ commit }, payload) {
      commit('CHANGECLOSED1', payload)
    },
    changeClosed2({ commit }, payload) {
      commit('CHANGECLOSED2', payload)
    },
    changeClosed3({ commit }, payload) {
      commit('CHANGECLOSED3', payload)
    },
    changeClosedHu({ commit }, payload) {
      commit('CHANGECLOSEDHU', payload)
    },
    changeClosedCommunity({ commit }, payload) {
      commit('CHANGECLOSEDCOMMUNITY', payload)
    },
    changeClosedBranch({ commit }, payload) {
      commit('CHANGECLOSEDBRANCH', payload)
    },
    changeClosedAge({ commit }, payload) {
      commit('CHANGECLOSEDAGE', payload)
    },
    
    changeClosedUpdate({ commit }, payload) {
      commit('CHANGEUPDATEDATA', payload)
    },
    changeAge({ commit }, payload) {
      commit('CHANGEAGE', payload)
    },
    changeNo({ commit }, payload) {
      commit('CHANGENO', payload)
    },
    // 网格层面
    changeGridMask({ commit }, payload) {
      commit('CHANGEGRIDMASK', payload)
    },
    changeGridClosed1({ commit }, payload) {
      commit('CHANGEGRIDCLOSED1', payload)
    },
    changeGridClosed2({ commit }, payload) {
      commit('CHANGEGRIDCLOSED2', payload)
    },
    changeGridClosedHuJi({ commit }, payload) {
      commit('CHANGEGRIDCLOSEDHUJI', payload)
    },
     changeGridClosedHu({ commit }, payload) {
      commit('CHANGEGRIDCLOSEDHU', payload)
    },
    changeGridClosedCommunity({ commit }, payload) {
      commit('CHANGEGRIDCLOSEDCOMMUNITY', payload)
    },
    changeGridClosedBranch({ commit }, payload) {
      commit('CHANGEGRIDCLOSEDBRANCH', payload)
    },
    changeGridClosedAge({ commit }, payload) {
      commit('CHANGEGRIDCLOSEDAGE', payload)
    },
    changeGridUpdateData({ commit }, payload) {
      commit('CHANGEGRIDUPDATEDATA', payload)
    },
     changeGridClosedRenYuan({ commit }, payload) {
      commit('CHANGEGRIDCLOSEDRENYUAN', payload)
    },
     changeGridClosedStudent({ commit }, payload) {
      commit('CHANGEGRIDCLOSEDSTUDENT', payload)
    },
     changeGridClosedSolier({ commit }, payload) {
      commit('CHANGEGRIDCLOSEDSOLIER', payload)
    },
     changeGridUpdateStudent({ commit }, payload) {
      commit('CHANGEGRIDUPDATESTUDENT', payload)
    },
    changeGridUpdateSolier({ commit }, payload) {
      commit('CHANGEGRIDUPDATESOLIER', payload)
    },
    changeGridStudent({ commit }, payload) {
      commit('CHANGEGRIDSTUDENT', payload)
    },
    changeGridSolier({ commit }, payload) {
      commit('CHANGEGRIDSOLIER', payload)
    },
    changeGridAge({ commit }, payload) {
      commit('CHANGEGRIDAGE', payload)
    },
    changeGridNo({ commit }, payload) {
      commit('CHANGEGRIDNO', payload)
    },


    // 小区层面
   changeCommunityMask({ commit }, payload) {
      commit('CHANGECOMMUNITYMASK', payload)
    },
    changeCommunityClosed1({ commit }, payload) {
      commit('CHANGECOMMUNITYCLOSED1', payload)
    },
    changeCommunityClosed2({ commit }, payload) {
      commit('CHANGECOMMUNITYCLOSED2', payload)
    },
    changeCommunityClosedHuJi({ commit }, payload) {
      commit('CHANGECOMMUNITYCLOSEDHUJI', payload)
    },
     changeCommunityClosedHu({ commit }, payload) {
      commit('CHANGECOMMUNITYCLOSEDHU', payload)
    },
    changeCommunityClosedAge({ commit }, payload) {
      commit('CHANGECOMMUNITYCLOSEDAGE', payload)
    },
    changeCommunityClosedStudent({ commit }, payload) {
      commit('CHANGECOMMUNITYCLOSEDSTUDENT', payload)
    },
    changeCommunityClosedSolier({ commit }, payload) {
      commit('CHANGECOMMUNITYCLOSEDSOLIER', payload)
    },
    changeCommunityNo({ commit }, payload) {
      commit('CHANGECOMMUNITYNO', payload)
    },
    changeCommunityAge({ commit }, payload) {
      commit('CHANGECOMMUNITYAGE', payload)
    },
    changeCommunityStudent({ commit }, payload) {
      console.log('aaaaaa',payload);
      commit('CHANGECOMMUNITYSTUDENT', payload)
    },
    changeCommunitySolier({ commit }, payload) {
      commit('CHANGECOMMUNITYSOLIER', payload)
    },
    changeCommunityClosedRenYuan({ commit }, payload) {
      commit('CHANGECOMMUNITYCLOSEDRENYUAN', payload)
    },
    changeCommunityUpdateData({ commit }, payload) {
      commit('CHANGECOMMUNITYUPDATEDATA', payload)
    },
    changeCommunityUpdateStudent({ commit }, payload) {
      commit('CHANGECOMMUNITYUPDATESTUDENT', payload)
    },
    changeCommunityUpdateSolier({ commit }, payload) {
      commit('CHANGECOMMUNITYUPDATESOLIER', payload)
    },



},
  getters: {},
})

export default store
