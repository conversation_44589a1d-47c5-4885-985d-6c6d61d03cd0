<template>
  <ScaleBox :width="1920" :height="1080" bgc="transparent" :delay="100" @scaleChange="changeScale">
    <div v-if="$route.path != '/login'">
      <div class="head">智慧则南中央数据管理平台</div>
      <div class="mengban" v-if="$route.name == 'Home' || $store.state.show.zhan"></div>
      <div class="mengban1" v-else-if="$route.name != 'Home' && !$store.state.show.zhan"></div>
      <div class="time_box">
        <div style="font-size: 22px; font-weight: bold">{{ time1 }}</div>
        <div style="font-size: 14px; color: white; padding-left: 21px">{{ time2 }}</div>
      </div>
      <div class="screen" v-if="!isAndroid">
        <img src="/full.png" alt="" @click="fullScreen" />
        <!-- <img src="/out.png" alt="" @click="exit"> -->
      </div>
      <div class="left_tianqi">
        <img src="/qing1.png" style="width: 30px; height: 30px" alt="" v-show="day.wea_img == 'qing'" />
        <img src="/dawu.png" style="width: 30px; height: 30px" alt="" v-show="day.wea_img == 'wu'" />
        <img src="/duoyun.png" style="width: 30px; height: 30px" alt="" v-show="day.wea_img == 'yun'" />
        <img src="/yu.png" style="width: 30px; height: 30px" alt="" v-show="day.wea_img == 'yu'" />
        <img src="/xue.png" style="width: 30px; height: 30px" alt="" v-show="day.wea_img == 'xue'" />
        <img src="/yin.png" style="width: 30px; height: 30px" alt="" v-show="day.wea_img == 'yin'" />
        <span v-if="day.tem_day != ''">&emsp;{{ day.tem_day }}℃</span>
      </div>
    </div>
    <div style="width: 100%; height: 100%">
      <router-view></router-view>
    </div>
  </ScaleBox>
</template>
<script setup>
import { onMounted, ref, reactive, toRefs } from 'vue'
import { getWeather } from './api'
import ScaleBox from 'vue3-scale-box'
import { useStore } from 'vuex' // 引入useStore 方法
import { useRouter } from 'vue-router'
import { useRoute } from 'vue-router'
import { pinyin } from 'pinyin-pro';
const route= useRoute()
// import { ElMessage, ElMessageBox } from 'element-plus'


const store = useStore() // 该方法用于返回store 实例
const router = useRouter()
let flag = ref(false)
let time1 = ref('')
let time2 = ref('')
let isAndroid = ref(false)
let day = reactive({
  week: '',
  wea: '',
  win: '',
  tem_day: '',
  tem_night: '',
  wea_img: '',
})
// 时间获取
function timeFn() {
  time1.value = filterTime1(new Date())
  time2.value = filterTime2(new Date())
}
// 过滤时分秒
function filterTime1(time) {
  var date = new Date(time)
  var m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  var d = date.getDate()
  d = d < 10 ? '0' + d : d
  var h = date.getHours()
  h = h < 10 ? '0' + h : h
  var minute = date.getMinutes()
  minute = minute < 10 ? '0' + minute : minute
  var s = date.getSeconds()
  s = s < 10 ? '0' + s : s
  return h + ':' + minute + ':' + s
}
// 过滤年月日
function filterTime2(time) {
  var date = new Date(time)
  var y = date.getFullYear()
  var m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  var d = date.getDate()
  d = d < 10 ? '0' + d : d
  var h = date.getHours()
  h = h < 10 ? '0' + h : h
  var minute = date.getMinutes()
  minute = minute < 10 ? '0' + minute : minute
  var s = date.getSeconds()
  s = s < 10 ? '0' + s : s
  return y + '.' + m + '.' + d
}
// 天气获取
// function getWeaAndTem() {
//   getWeather({
//     appid: '28155953', // 账号ID
//     appsecret: 'IZN4plax', // 账号密钥
//     cityid: '', // 城市编号,不要带CN, 以下参数3选1，当前以西湖区举例
//     city: '广元', // 城市名称,不要带市和区
//     ip: null, // IP地址
//     callback: 0, // jsonp方式
//   })
//     .then((res) => {
//       // if (res. === 200) {
//       day = {
//         week: res.week,
//         wea: res.wea,
//         win: res.win,
//         tem_day: res.tem_day,
//         tem_night: res.tem_night,
//         wea_img: res.wea_img,
//       }
//     })
//     .catch((error) => {
//       console.log(error)
//     })
// }
function changeScale(e) {
  store.dispatch('changeScale', e)
}
function fullScreen() {
  let element = document.documentElement
  // 不全屏是null,返回false,
  flag.value = document.fullscreenElement === null ? false : true
  // false是进入全屏状态
  if (flag.value) {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  } else {
    // 全屏
    if (element.requestFullscreen) {
      element.requestFullscreen()
    }
  }
  // 切换文本状态（只是用在文本上，文本不是动态可以忽略）
  flag.value = !flag.value
}

onMounted(() => {
  // 初始化设置
  isAndroid.value = /android/i.test(navigator.userAgent);
  
  // 设置定时器
  setInterval(timeFn, 1000);

  // 完整命令映射配置
  const COMMAND_MAPPINGS = {
    // 通用命令（所有页面都适用的命令）
    GLOBAL: {
      '打开上西则南社区': { 
        action: () => router.push('/grid'), 
        message: '已进入上西则南社区' 
      },
      '打开数据看板': { 
        action: () => store.dispatch('openZhan'), 
        message: '已打开数据看板' 
      },
      '关闭数据看板': { 
        action: () => store.dispatch('closeZhan'), 
        message: '已关闭数据看板' 
      },
      '返回': { 
        action: () => handleBackCommand(), 
        message: '正在返回' 
      }
    },
    
    // 网格页面命令 (/grid)
    GRID: {
      // 网格导航命令
      '望凤街': { 
        action: () => router.push({ path: '/communities', query: { code: 10 } }), 
        message: '已进入望凤街网格' ,
      },
      '中兴街': { 
        action: () => router.push({ path: '/communities', query: { code: 20 } }), 
        message: '已进入中兴街网格' ,

      },
      
      // 网格开关命令
      '打开党支部': { 
        actions: [
          ['changeMask', true], 
          ['changeClosedBranch', true]
        ], 
        message: '已打开党支部' ,
      },
      '关闭党支部': { 
        actions: [
          ['changeMask', false], 
          ['changeClosedBranch', false]
        ], 
        message: '已关闭党支部' 
      },
      '打开总户数': { 
        actions: [
          ['changeMask', true], 
          ['changeClosedHu', true]
        ], 
        message: '已打开总户数' 
      },
      '关闭总户数': { 
        actions: [
          ['changeMask', false], 
          ['changeClosedHu', false]
        ], 
        message: '已关闭总户数' 
      },
      '打开总人数': { 
        actions: [
          ['changeMask', true], 
          ['changeClosed2', true]
        ], 
        message: '已打开总人数' 
      },
      '关闭总人数': { 
        actions: [
          ['changeMask', false], 
          ['changeClosed2', false]
        ], 
        message: '已关闭总人数' 
      },
      '打开小区': { 
        actions: [
          ['changeMask', true], 
          ['changeClosedCommunity', true]
        ], 
        message: '已打开小区' 
      },
      '关闭小区': { 
        actions: [
          ['changeMask', false], 
          ['changeClosedCommunity', false]
        ], 
        message: '已关闭小区' 
      },
      '打开特殊人群': { 
        actions: [
          ['changeNo', 2],
          ['changeMask', true], 
          ['changeClosed1', true]
        ], 
        message: '已打开特殊人群' 
      },
      '关闭特殊人群': { 
        actions: [
          ['changeMask', false], 
          ['changeClosed1', false]
        ], 
        message: '已关闭特殊人群' 
      },
      '打开社区名人': { 
        actions: [
          ['changeNo', 1],
          ['changeMask', true], 
          ['changeClosed1', true]
        ], 
        message: '已打开社区名人' 
      },
      '关闭社区名人': { 
        actions: [
          ['changeMask', false], 
          ['changeClosed1', false]
        ], 
        message: '已关闭社区名人' 
      },
      '打开军人构成': { 
        actions: [
          ['changeNo', 3],
          ['changeMask', true], 
          ['changeClosed1', true]
        ], 
        message: '已打开军人构成' 
      },
      '关闭军人构成': { 
        actions: [
          ['changeMask', false], 
          ['changeClosed1', false]
        ], 
        message: '已关闭军人构成' 
      },
      '打开事业单位': { 
        actions: [
          ['changeNo', 4],
          ['changeMask', true], 
          ['changeClosed1', true]
        ], 
        message: '已打开事业单位' 
      },
      '关闭事业单位': { 
        actions: [
          ['changeMask', false], 
          ['changeClosed1', false]
        ], 
        message: '已关闭事业单位' 
      },
      '打开社会人员': { 
        actions: [
          ['changeMask', true], 
          ['changeClosed3', true]
        ], 
        message: '已打开社会人员' 
      },
      '关闭社会人员': { 
        actions: [
          ['changeMask', false], 
          ['changeClosed3', false]
        ], 
        message: '已关闭社会人员' 
      },
      '打开年龄结构': { 
        actions: [
          ['changeAge', 1],
          ['changeMask', true], 
          ['changeClosedAge', true],
          ['changeClosedUpdate', true]
        ], 
        message: '已打开年龄结构' 
      },
      '关闭年龄结构': {
        actions: [
          ['changeMask', false], 
          ['changeClosedAge', false],
          ['changeClosedUpdate', false]
        ], 
        message: '已关闭年龄结构' 
      }
    },
    
    // 社区页面命令 (/communities)
    COMMUNITIES: {
      '打开网格党支部': { 
        actions: [
          ['changeGridMask', true], 
          ['changeGridClosedBranch', true]
        ], 
        message: '已打开网格党支部' 
      },
      '关闭网格党支部': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosedBranch', false]
        ], 
        message: '已关闭网格党支部' 
      },
      '打开网格户数': { 
        actions: [
          ['changeGridMask', true], 
          ['changeGridClosedHu', true]
        ], 
        message: '已打开网格户数' 
      },
      '关闭网格户数': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosedHu', false]
        ], 
        message: '已关闭网格户数' 
      },
      '打开网格人数': { 
        actions: [
          ['changeGridMask', true], 
          ['changeGridClosed2', true]
        ], 
        message: '已打开网格人数' 
      },
      '关闭网格人数': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosed2', false]
        ], 
        message: '已关闭网格人数' 
      },
      '打开网格小区': { 
        actions: [
          ['changeGridMask', true], 
          ['changeGridClosedCommunity', true]
        ], 
        message: '已打开网格小区' 
      },
      '关闭网格小区': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosedCommunity', false]
        ], 
        message: '已关闭网格小区' 
      },
      '打开特殊人群': { 
        actions: [
          ['changeGridNo', 2],
          ['changeGridMask', true], 
          ['changeGridClosed1', true]
        ], 
        message: '已打开特殊人群' 
      },
      '关闭特殊人群': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosed1', false]
        ], 
        message: '已关闭特殊人群' 
      },
      '打开社区名人': { 
        actions: [
          ['changeGridNo', 1],
          ['changeGridMask', true], 
          ['changeGridClosed1', true]
        ], 
        message: '已打开社区名人' 
      },
      '关闭社区名人': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosed1', false]
        ], 
        message: '已关闭社区名人' 
      },
      '打开军人构成': { 
        actions: [
          ['changeGridNo', 3],
          ['changeGridMask', true], 
          ['changeGridClosed1', true]
        ], 
        message: '已打开军人构成' 
      },
      '关闭军人构成': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosed1', false]
        ], 
        message: '已关闭军人构成' 
      },
      '打开事业单位': { 
        actions: [
          ['changeGridNo', 4],
          ['changeGridMask', true], 
          ['changeGridClosed1', true]
        ], 
        message: '已打开事业单位' 
      },
      '关闭事业单位': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosed1', false]
        ], 
        message: '已关闭事业单位' 
      },
      '打开户籍情况': { 
        actions: [
          ['changeGridMask', true], 
          ['changeGridClosedHuJi', true]
        ], 
        message: '已打开网格户籍情况' 
      },
      '关闭户籍情况': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosedHuJi', false]
        ], 
        message: '已关闭网格户籍情况' 
      },
      '打开人员类型': { 
        actions: [
          ['changeGridMask', true], 
          ['changeGridClosedRenYuan', true]
        ], 
        message: '已打开人员类型' 
      },
      '关闭人员类型': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosedRenYuan', false]
        ], 
        message: '已关闭人员类型' 
      },
      '打开年龄结构': { 
        actions: [
          ['changeGridAge', 1],
          ['changeGridMask', true], 
          ['changeGridClosedAge', true],
          ['changeGridUpdateData', true]
        ], 
        message: '已打开年龄结构' 
      },
      '关闭年龄结构': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosedAge', false],
          ['changeGridUpdateData', false]
        ], 
        message: '已关闭年龄结构' 
      },
      '打开学生构成': { 
        actions: [
          ['changeGridStudent', 1],
          ['changeGridMask', true], 
          ['changeGridClosedStudent', true],
          ['changeGridUpdateStudent', true]
        ], 
        message: '已打开学生构成' 
      },
      '关闭学生构成': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosedStudent', false],
          ['changeGridUpdateStudent', false]
        ], 
        message: '已关闭学生构成' 
      },
      '打开重点监管人群': { 
        actions: [
          ['changeGridSolier', 1],
          ['changeGridMask', true], 
          ['changeGridClosedSolier', true],
          ['changeGridUpdateSolier', true]
        ], 
        message: '已打开重点监管人群' 
      },
      '关闭重点监管人群': { 
        actions: [
          ['changeGridMask', false], 
          ['changeGridClosedSolier', false],
          ['changeGridUpdateSolier', false]
        ], 
        message: '已关闭重点监管人群' 
      }
    },
    
    // 特定小区页面命令 (/specificCommunity)
    SPECIFIC_COMMUNITY: {
      '打开小区户数': { 
        actions: [
          ['changeCommunityMask', true], 
          ['changeCommunityClosedHu', true]
        ], 
        message: '已打开小区户数' 
      },
      '关闭小区户数': { 
        actions: [
          ['changeCommunityMask', false], 
          ['changeCommunityClosedHu', false]
        ], 
        message: '已关闭小区户数' 
      },
      '打开小区人数': { 
        actions: [
          ['changeCommunityMask', true], 
          ['changeCommunityClosed2', true]
        ], 
        message: '已打开小区人数' 
      },
      '关闭小区人数': { 
        actions: [
          ['changeCommunityMask', false], 
          ['changeCommunityClosed2', false]
        ], 
        message: '已关闭小区人数' 
      },
      '打开特殊人群': { 
        actions: [
          ['changeCommunityNo', 2],
          ['changeCommunityMask', true], 
          ['changeCommunityClosed1', true]
        ], 
        message: '已打开特殊人群' 
      },
      '关闭特殊人群': { 
        actions: [
          ['changeCommunityMask', false], 
          ['changeCommunityClosed1', false]
        ], 
        message: '已关闭特殊人群' 
      },
      '打开社区名人': { 
        actions: [
          ['changeCommunityNo', 1],
          ['changeCommunityMask', true], 
          ['changeCommunityClosed1', true]
        ], 
        message: '已打开社区名人' 
      },
      '关闭社区名人': { 
        actions: [
          ['changeCommunityMask', false], 
          ['changeCommunityClosed1', false]
        ], 
        message: '已关闭社区名人' 
      },
      '打开军人构成': { 
        actions: [
          ['changeCommunityNo', 3],
          ['changeCommunityMask', true], 
          ['changeCommunityClosed1', true]
        ], 
        message: '已打开军人构成' 
      },
      '关闭军人构成': { 
        actions: [
          ['changeCommunityMask', false], 
          ['changeCommunityClosed1', false]
        ], 
        message: '已关闭军人构成' 
      },
      '打开小区户籍情况': { 
        actions: [
          ['changeCommunityMask', true], 
          ['changeCommunityClosedHuJi', true]
        ], 
        message: '已打开小区户籍情况' 
      },
      '关闭小区户籍情况': { 
        actions: [
          ['changeCommunityMask', false], 
          ['changeCommunityClosedHuJi', false]
        ], 
        message: '已关闭小区户籍情况' 
      },
      '打开小区人员类型': { 
        actions: [
          ['changeCommunityMask', true], 
          ['changeCommunityClosedRenYuan', true]
        ], 
        message: '已打开小区人员类型' 
      },
      '关闭小区人员类型': { 
        actions: [
          ['changeCommunityMask', false], 
          ['changeCommunityClosedRenYuan', false]
        ], 
        message: '已关闭小区人员类型' 
      },
      '打开年龄结构': { 
        actions: [
          ['changeCommunityAge', 1],
          ['changeCommunityMask', true], 
          ['changeCommunityClosedAge', true],
          ['changeCommunityUpdateData', true]
        ], 
        message: '已打开年龄结构' 
      },
      '关闭年龄结构': { 
        actions: [
          ['changeCommunityMask', false], 
          ['changeCommunityClosedAge', false],
          ['changeCommunityUpdateData', false]
        ], 
        message: '已关闭年龄结构' 
      },
      '打开学生构成': { 
        actions: [
          ['changeCommunityStudent', 1],
          ['changeCommunityMask', true], 
          ['changeCommunityClosedStudent', true],
          ['changeCommunityUpdateStudent', true]
        ], 
        message: '已打开学生构成' 
      },
      '关闭学生构成': { 
        actions: [
          ['changeCommunityMask', false], 
          ['changeCommunityClosedStudent', false],
          ['changeCommunityUpdateStudent', false]
        ], 
        message: '已关闭学生构成' 
      },
      '打开重点监管人群': { 
        actions: [
          ['changeCommunitySolier', 1],
          ['changeCommunityMask', true], 
          ['changeCommunityClosedSolier', true],
          ['changeCommunityUpdateSolier', true]
        ], 
        message: '已打开重点监管人群' 
      },
      '关闭重点监管人群': { 
        actions: [
          ['changeCommunityMask', false], 
          ['changeCommunityClosedSolier', false],
          ['changeCommunityUpdateSolier', false]
        ], 
        message: '已关闭重点监管人群' 
      }
    }
  };

  // 处理返回命令
  const handleBackCommand = () => {
    store.dispatch('closeZhan');
    if (route.path === '/home') {
      if (window?.history) {
        window.history.back();
      } else if (parent?.history) {
        parent.history.back();
      } else {
        router.back();
      }
    } else {
      router.back();
    }
  };

  // 处理社区跳转
  const handleCommunityNavigation = (command) => {
    if (route.query.code == 20) {
      store.state.grid.zhongXing.forEach(v => {
        if (command.includes(pinyin(v.text, { toneType: 'none', type: 'string' }).replace(/\s+/g, ''))) {
          router.push({
            path: '/specificCommunity',
            query: { code: v.infoId }
          });
        }
      });
    } else {
      store.state.grid.wangFeng.forEach(v => {
        if (command.includes(pinyin(v.text, { toneType: 'none', type: 'string' }).replace(/\s+/g, ''))) {
          router.push({
            path: '/specificCommunity',
            query: { code: v.infoId }
          });
        }
      });
    }
  };

  // 执行动作并返回确认消息
  const executeActions = (config) => {
    if (config.action) {
      config.action();
    } else if (config.actions) {
      config.actions.forEach(([action, value]) => {
        store.dispatch(action, value);
      });
    }
    return config.message;
  };

  // 处理语音命令
  const handleVoiceCommand = (command, currentRoute) => {
    let ackMsg = '';
    
    // 1. 处理全局命令
    for (const [keyword, config] of Object.entries(COMMAND_MAPPINGS.GLOBAL)) {
      // if (command.includes(keyword)) {
      //   ackMsg = executeActions(config);
      //   return ackMsg;
      // }
      let p = pinyin(keyword, { toneType: 'none', type: 'string' }).replace(/\s+/g, '');
      if (command.includes(p)) {
        ackMsg = executeActions(config);
        return ackMsg;
      }
    }
    
    // 2. 根据当前路由处理特定命令
    switch (currentRoute) {
      case '/grid':
        for (const [keyword, config] of Object.entries(COMMAND_MAPPINGS.GRID)) {
          // if (command.includes(keyword)) {
          //   ackMsg = executeActions(config);
          //   return ackMsg;
          // }
          if (command.includes(pinyin(keyword, { toneType: 'none', type: 'string' }).replace(/\s+/g, ''))) {
            ackMsg = executeActions(config);
            return ackMsg;
          }
        }
        break;
        
      case '/communities':
        // 处理社区跳转
        handleCommunityNavigation(command);
        
        // 处理社区命令
        for (const [keyword, config] of Object.entries(COMMAND_MAPPINGS.COMMUNITIES)) {
          // if (command.includes(keyword)) {
          //   ackMsg = executeActions(config);
          //   return ackMsg;
          // }
          if (command.includes(pinyin(keyword, { toneType: 'none', type: 'string' }).replace(/\s+/g, ''))) {
            ackMsg = executeActions(config);
            return ackMsg;
          }
        }
        break;
        
      case '/specificCommunity':
        for (const [keyword, config] of Object.entries(COMMAND_MAPPINGS.SPECIFIC_COMMUNITY)) {
          // if (command.includes(keyword)) {
          //   ackMsg = executeActions(config);
          //   return ackMsg;
          // }
          if (command.includes(pinyin(keyword, { toneType: 'none', type: 'string' }).replace(/\s+/g, ''))) {
            ackMsg = executeActions(config);
            return ackMsg;
          }
        }
        break;
    }
    
    return ackMsg;
  };

  // 消息事件监听
  window.addEventListener('message', (event) => {
    console.log('ioc收到消息:', event);
    
    // 验证消息来源
    // if (event.origin !== 'https://ioc.zn.nextv.show/') {
    //   console.log('消息来源不匹配:', event.origin);
    //   return;
    // }
    
    // 处理语音命令
    if (event.data && event.data.source === 'zn-app') {
      if (event.data.type === 'voice-command') {
        console.log('ioc收到语音指令:', event.data.data.command);
        const command = event.data.data.command;
        const currentRoute = route.path;
        
        let ackMsg = '';
        if (command) {
          ackMsg = handleVoiceCommand(command, currentRoute);
        }
        
        // 发送确认回执
        window.parent.postMessage({
          type: 'voice-command-ack',
          reqId: event.data.reqId,
          result: ackMsg
        }, event.origin);
      }
    }
  });
});
</script>
<style scoped lang="scss">
// @font-face {
//   font-family: title;
//   src: url('/title.TTF')
// }

.screen {
  width: 80px;
  height: 30px;
  position: absolute;
  right: 1%;
  top: 4%;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  cursor: pointer;

  img {
    width: 30px;
    height: 30px;
  }
}

.head {
  position: absolute;
  top: 0px;
  width: 100%;
  background: url(/head.png) no-repeat;
  background-size: 100%;
  height: 100px;
  z-index: 2;
  font-size: 36px;
  color: white;
  text-align: center;
  letter-spacing: 8px;
  line-height: 60px;
  font-weight: bold;
  // font-family: title;
}

.mengban {
  position: absolute;
  width: 100%;
  background: url(/mengban01.png) no-repeat;
  background-size: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.mengban1 {
  position: absolute;
  width: 100%;
  background: url(/mengban03.png) no-repeat;
  background-size: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.time_box {
  width: 110px;
  height: 49px;
  color: white;
  position: absolute;
  z-index: 2;
  right: 100px;
  top: 34px;
  border-right: 1px solid #a7a7a7;
}

.left_tianqi {
  display: flex;
  position: absolute;
  margin-right: 20px;
  top: 34px;
  left: 50px;
  width: 120px;
  height: 30px;
  z-index: 2;
  color: white;
  font-weight: bold;
  font-size: 18px;
}
</style>
