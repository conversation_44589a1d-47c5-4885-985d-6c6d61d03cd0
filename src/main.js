import { createApp } from 'vue'
import './style.scss'
import App from './App.vue'
//UI
import ElementPlus from './plugins/elementplus'
//路由
import router from './router'
//状态
import store from './store'
import * as Cesium from 'cesium'

// 添加全局 Cesium 错误处理
if (typeof Cesium !== 'undefined') {
  // 捕获 Cesium 渲染错误
  window.addEventListener('error', (event) => {
    if (event.error && event.error.message &&
        (event.error.message.includes('updateTransform') ||
         event.error.message.includes('Cesium') ||
         event.error.message.includes('rendering'))) {
      console.warn('Cesium rendering error caught and handled:', event.error)
      event.preventDefault() // 阻止错误传播
      return false
    }
  })

  // 捕获未处理的 Promise 拒绝
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message &&
        event.reason.message.includes('Cesium')) {
      console.warn('Cesium promise rejection caught and handled:', event.reason)
      event.preventDefault()
    }
  })
}

createApp(App).use(router).use(store).use(ElementPlus).mount('#app')
