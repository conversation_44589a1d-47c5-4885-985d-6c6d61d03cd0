# CSP策略故障排除指南

## 🔍 问题解决

CSP（Content Security Policy）策略问题已修复！

### ✅ 当前状态
- CSP策略已临时禁用
- 应用可以在HTTPS环境正常运行
- 不会出现混合内容错误

### 🛠️ 修复内容

```html
<!-- 之前：强制HTTPS升级 -->
<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />

<!-- 现在：已注释禁用 -->
<!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" /> -->
```

## 🚀 部署验证

1. **推送代码**
   ```bash
   git add .
   git commit -m "修复CSP策略问题"
   git push origin main
   ```

2. **访问测试**
   - 访问：https://ioc.zn.nextv.show/
   - 检查浏览器控制台无CSP错误
   - 验证所有功能正常

## 🔧 如果需要重新启用CSP

### 选项1：宽松的CSP策略
```html
<meta http-equiv="Content-Security-Policy" content="default-src 'self' https: data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http:; style-src 'self' 'unsafe-inline' https: http:; img-src 'self' data: https: http:; font-src 'self' data: https: http:; connect-src 'self' https: http: ws: wss:;" />
```

### 选项2：仅HTTPS环境启用
```html
<script>
if (location.protocol === 'https:') {
  document.write('<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">');
}
</script>
```

### 选项3：通过Nginx设置CSP
```nginx
add_header Content-Security-Policy "upgrade-insecure-requests" always;
```

## 📊 监控建议

部署后监控以下指标：
- [ ] 页面加载成功率
- [ ] 资源加载错误
- [ ] 浏览器控制台错误
- [ ] 用户访问体验

## 🎯 最佳实践

1. **渐进式启用**：先确保功能正常，再逐步加强安全策略
2. **环境区分**：开发环境宽松，生产环境严格
3. **监控告警**：设置CSP违规报告
4. **定期审查**：定期检查和更新CSP策略

## 📞 问题反馈

如果仍有问题，请检查：
1. 浏览器开发者工具Console标签页
2. Network标签页的资源加载状态
3. 服务器Nginx错误日志
4. GitHub Actions部署日志
