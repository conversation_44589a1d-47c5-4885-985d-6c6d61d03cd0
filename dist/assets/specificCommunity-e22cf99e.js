import{_ as I,r as i,o as j,b as m,c as v,d as e,t as D,F as B,j as U,s as V,f as q,E as P,g as G,w as S,e as k,p as _,x as w,i as g,l as K}from"./index-e6e213aa.js";import{j as M,L as X,i as L,s as Z,l as Q,t as ee,q as te,r as ae,m as oe,n as se,o as ne,p as ie}from"./di02-e3875920.js";import{a as le,p as re,s as de,b as ue,c as ce,h as me,r as fe}from"./ageConstruction-5c8b4794.js";const ge={class:"_box"},pe={class:"top"},he={class:"top"},ye={class:"top"},be={class:"top"},ve={class:"_box2"},Ce={__name:"community",props:{householder:Number,user_sum:Number,dataHuJi:Array,dataType:Array,dataSex:Array,dataFace:Array,building:Number,unit:Number},emits:["increase"],setup(T,{emit:F}){const t=F,l=T,H=i(null),A=i(null),x=i(null),z=i(null);j(()=>{setTimeout(()=>{o(),J(),O(),R()},1e3)});function o(){const p=M(H.value),a=[[{offset:0,color:"rgba(16, 115, 255, 1)"},{offset:1,color:"rgba(16, 115, 255, 1)"}],[{offset:0,color:"rgba(54, 234, 255, 1)"},{offset:1,color:"rgba(10, 165, 214, 1)"}]],u=["rgba(50, 255, 238, 1)","rgba(0, 233, 179, 1)","rgba(29, 246, 66, 1)","rgba(240, 255, 71, 1)","rgba(255, 213, 47, 1)","rgba(255, 126, 76, 1)","rgba(255, 96, 86, 1)","rgba(97, 187, 255, 1)"],y=l.dataHuJi.map((d,b)=>({name:d.name,value:d.num,itemStyle:{color:{type:"linear",colorStops:a[b]}}}));var r={title:{text:"户籍情况",left:"center",textStyle:{color:"#fff"},top:14},series:[{type:"pie",radius:["30%","50%"],center:["50%","50%"],selectedMode:!1,hoverAnimation:!1,top:5,data:[{value:1,name:""}],itemStyle:{color:"#0e2c5f"},label:{show:!1},labelLine:{show:!1},tooltip:{show:!1}},{name:"户籍情况",type:"pie",radius:["20%","40%"],center:["50%","50%"],avoidLabelOverlap:!1,labelLine:{length:20,length2:100},top:5,label:{formatter:function(d){return"{a|"+d.data.name+` }

 {b|`+d.data.value+"}"},padding:[0,-110,-10,-110],rich:{a:{fontSize:14,color:"#fff",fontWeight:500,align:"center"},b:{fontSize:16,color:"#fff",fontWeight:"bold",align:"center"}}},data:y}],color:u,backgroundColor:"transparent"};r&&p.setOption(r)}function J(){const p=M(A.value),a=[[{offset:0,color:"rgba(16, 115, 255, 1)"},{offset:1,color:"rgba(16, 115, 255, 1)"}],[{offset:0,color:"rgba(54, 234, 255, 1)"},{offset:1,color:"rgba(10, 165, 214, 1)"}]],u=["rgba(50, 255, 238, 1)","rgba(0, 233, 179, 1)","rgba(29, 246, 66, 1)","rgba(240, 255, 71, 1)","rgba(255, 213, 47, 1)","rgba(255, 126, 76, 1)","rgba(255, 96, 86, 1)","rgba(97, 187, 255, 1)"],y=l.dataType.map((d,b)=>({name:d.name,value:d.num,itemStyle:{color:{type:"linear",colorStops:a[b]}}}));var r={title:{text:"人员类型",left:"center",textStyle:{color:"#fff"},top:14},series:[{type:"pie",radius:["30%","50%"],center:["50%","50%"],selectedMode:!1,hoverAnimation:!1,top:5,data:[{value:1,name:""}],itemStyle:{color:"#0e2c5f"},label:{show:!1},labelLine:{show:!1},tooltip:{show:!1}},{name:"人员类型",type:"pie",top:5,radius:["20%","40%"],center:["50%","50%"],avoidLabelOverlap:!1,labelLine:{length:20,length2:80},label:{formatter:function(d){return"{a|"+d.data.name+` }

 {b|`+d.data.value+"}"},padding:[0,-80,-10,-80],rich:{a:{fontSize:14,color:"#fff",fontWeight:500,align:"center"},b:{fontSize:16,color:"#fff",fontWeight:"bold",align:"center"}}},data:y}],color:u,backgroundColor:"transparent"};r&&p.setOption(r)}function O(){const p=M(x.value),a=i(0);a.value=l.dataFace.reduce((c,r)=>c+=r.value,0),a.value==0||a.value;function u(c,r){let d=r||"value",b=[];return c&&c.forEach(function($){b.push($[d])}),b}var y={title:{text:"政治面貌",textStyle:{color:"#fff"},left:110},backgroundColor:"transparent",grid:{top:"20%",bottom:0,left:30,containLabel:!0},xAxis:{show:!1},yAxis:[{triggerEvent:!0,show:!0,inverse:!0,data:u(l.dataFace,"name"),axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1,interval:0,color:"red",align:"left",margin:80,fontSize:13,formatter:function(c,r){return"{title|"+c+"}"},rich:{title:{width:165}}}},{triggerEvent:!0,show:!0,inverse:!0,data:u(l.dataFace,"name"),axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{interval:0,shadowOffsetX:"-20px",color:"#0f69f1",fontWeight:"bold",align:"right",verticalAlign:"bottom",lineHeight:30,fontSize:14,fontStyle:"italic",formatter:function(c,r){return l.dataFace[r].value==0?"0%":(l.dataFace[r].value/a.value*100).toFixed(1)+"%"}}}],series:[{name:"条",type:"bar",showBackground:!0,backgroundStyle:{borderRadius:30},barBorderRadius:30,yAxisIndex:0,data:l.dataFace,barWidth:9,itemStyle:{color:new X(0,0,1,0,[{offset:0,color:"#0f69f1"},{offset:1,color:"#81eff3"}],!1),barBorderRadius:10,barBorderRadius:4},label:{normal:{show:!0,color:"#fff",position:[0,"-20px"],textStyle:{fontSize:14},formatter:function(c,r){return` ${c.name}  {hou|${c.value}} 人`},rich:{hou:{fontWeight:"bold",fontSize:16}}}}}]};y&&p.setOption(y)}function R(){const p=M(z.value);let a=l.dataSex.reduce((f,C)=>f+C.num,0);console.log(l.dataSex);const u=[[{offset:0,color:"rgba(15, 105, 241, 1)"},{offset:1,color:"rgba(15, 105, 241, 1)"}],[{offset:0,color:"rgba(54, 234, 255, 1)"},{offset:1,color:"rgba(10, 165, 214, 1)"}]],y=["rgba(15, 105, 241,.3)","rgba(0, 56, 75, 1)"],c=["rgba(50, 255, 238, 1)","rgba(0, 233, 179, 1)","rgba(29, 246, 66, 1)","rgba(240, 255, 71, 1)","rgba(255, 213, 47, 1)","rgba(255, 126, 76, 1)","rgba(255, 96, 86, 1)","rgba(97, 187, 255, 1)"],r=l.dataSex.map((f,C)=>({name:f.name,value:f.num,itemStyle:{color:{type:"linear",colorStops:u[C]}}})),d=l.dataSex.map((f,C)=>({name:f.name,value:f.num,itemStyle:{color:y[C]}}));var $={title:{text:"性别构成",left:"center",textStyle:{color:"#fff"}},series:[{name:"性别比例",type:"pie",center:["50%","50%"],radius:[0,"50%"],top:20,label:{fontSize:14,position:"inner",formatter:f=>`${f.name}

${f.value}`,color:"white"},selectedMode:"single",data:d},{name:"性别比例",type:"pie",top:20,radius:["50%","80%"],center:["50%","50%"],avoidLabelOverlap:!1,label:{show:!0,position:"inside",fontSize:16,formatter:f=>`${(f.data.value/a*100).toFixed(0)}%`,color:"white",fontWeight:"bold"},data:r}],color:c,backgroundColor:"transparent"};$&&p.setOption($)}function h(p){switch(p){case 6:t("dialogRenYuan");break;case 5:t("dialogHuJi");break;case 4:t("dialog2");break;case 3:break;case 2:break;case 1:t("dialogHu");break}}return(p,a)=>(m(),v(B,null,[e("div",ge,[e("div",{class:"single",onClick:a[0]||(a[0]=u=>h(1))},[e("div",pe,D(l.householder),1),a[6]||(a[6]=e("img",{src:L,alt:""},null,-1)),a[7]||(a[7]=e("div",{class:"bottom"},"户数（户）",-1))]),e("div",{class:"single",onClick:a[1]||(a[1]=u=>h(2))},[e("div",he,D(l.building),1),a[8]||(a[8]=e("img",{src:L,alt:""},null,-1)),a[9]||(a[9]=e("div",{class:"bottom"},"栋数（栋）",-1))]),e("div",{class:"single",onClick:a[2]||(a[2]=u=>h(3))},[e("div",ye,D(l.unit),1),a[10]||(a[10]=e("img",{src:L,alt:""},null,-1)),a[11]||(a[11]=e("div",{class:"bottom"},"单元（个）",-1))]),e("div",{class:"single",onClick:a[3]||(a[3]=u=>h(4))},[e("div",be,D(l.user_sum),1),a[12]||(a[12]=e("img",{src:L,alt:""},null,-1)),a[13]||(a[13]=e("div",{class:"bottom"},"人数（人）",-1))])]),e("div",ve,[e("div",{ref_key:"huji",ref:H,id:"huji",onClick:a[4]||(a[4]=u=>h(5))},null,512),e("div",{ref_key:"renyuan",ref:A,id:"renyuan",onClick:a[5]||(a[5]=u=>h(6))},null,512),e("div",{ref_key:"zhengzhi",ref:x,id:"zhengzhi"},null,512),e("div",{ref_key:"sex",ref:z,id:"sex"},null,512)])],64))}},_e=I(Ce,[["__scopeId","data-v-db35f811"]]);const Se={src:se,alt:""},ke={src:ne,alt:""},$e={class:"biao"},we=["src"],De={class:"left"},Ae={class:"one"},He={class:"one_dis"},xe={class:"right"},ze={class:"one"},Me={class:"one_dis"},Le={class:"two"},Fe={class:"two_dis"},Je={class:"bottom"},Oe={class:"bot_dis"},Re={class:"mengban"},Te={class:"dialog"},Ie={key:0,class:"dialog"},je={key:1,class:"dialog"},Be={key:2,class:"dialog"},Ee={key:3,class:"dialog"},Ye={key:4,class:"dialog"},Ne={key:5,class:"dialog"},We={key:6,class:"dialog"},Ue={__name:"specificCommunity",setup(T){const F=U(),t=V();let l=i("");const H=i(1),A=i(""),x=i(!1);i(!1),i(!1),i(!1),i(!1),i(!1),i(!1),i(!1);const z=i(!1);i(!1),i(!1),i(!1),i(!1);const o=q({region_name:"",householder:0,building:0,unit:0,user_sum:0,subObj:{},dataAge:[],list:[{value1:340,value2:"户数（户）"},{value1:10,value2:"栋数（栋）"},{value1:10,value2:"单元（个）"},{value1:5623,value2:"人数（人）"}],dataSex:[],dataFace:[],dataType:[],dataHuJi:[],dataStudent:[{name:"大学",value:200},{name:"高中",value:150},{name:"初中",value:200},{name:"小学",value:100}],dataSolier:[],dataPerson:[],dataSpecial:[],dataImportant:[],dataInteraction:{data1:[10,5,8,2,20,5,10,6],data2:[20,10,5,3,10,5,20,6],data3:[4,20,6,10,5,20,6,10],data4:[5,10,6,20,2,10,5,6]},focusAge:null,focusStudent:null,focusSolier:null});j(()=>{l.value=F.query.code,l.value&&J(l.value)});async function J(s){const n=await Z({type:"3",code:s});n.code==200?(o.region_name=n.data.region_name,o.householder=n.data.householder,o.user_sum=n.data.user_sum,o.building=n.data.building,o.unit=n.data.unit,o.dataHuJi=n.data.domicile_from,o.dataType=n.data.domicile_type,o.dataSex=n.data.gender,o.dataFace=n.data.political,o.dataAge=n.data.age,o.dataPerson=n.data.famous_type,o.dataSpecial=n.data.special_group,o.dataImportant=n.data.focus_type,o.dataSolier=n.data.soldier_type,o.dataStudent=n.data.student,o.subObj={teenager:n.data.teenager,teenager_percent:n.data.teenager_percent,middle_aged:n.data.middle_aged,middle_aged_percent:n.data.middle_aged_percent,old_aged:n.data.old_aged,old_aged_percent:n.data.old_aged_percent},A.value=n.data.vr_url):P.error(n.message)}function O(){t.dispatch("changeZhan")}function R(s){H.value=s,t.dispatch("changeCommunityMask",!0),t.dispatch("changeCommunityClosed1",!0)}function h(){t.dispatch("changeCommunityMask",!1),t.dispatch("changeCommunityClosed1",!1)}function p(s){o.focusAge=s,t.dispatch("changeCommunityMask",!0),t.dispatch("changeCommunityClosedAge",!0),t.dispatch("changeCommunityUpdateData",!0)}function a(){t.dispatch("changeCommunityMask",!1),t.dispatch("changeCommunityClosedAge",!1),t.dispatch("changeCommunityUpdateData",!1)}function u(){t.dispatch("changeCommunityMask",!0),t.dispatch("changeCommunityClosedHu",!0)}function y(){t.dispatch("changeCommunityMask",!1),t.dispatch("changeCommunityClosedHu",!1)}function c(){x.value=!0,z.value=!0}function r(){t.dispatch("changeCommunityMask",!0),t.dispatch("changeCommunityClosed2",!0)}function d(){t.dispatch("changeCommunityMask",!1),t.dispatch("changeCommunityClosed2",!1)}function b(){t.dispatch("changeCommunityMask",!0),t.dispatch("changeCommunityClosedHuJi",!0)}function $(){t.dispatch("changeCommunityMask",!1),t.dispatch("changeCommunityClosedHuJi",!1)}function f(){t.dispatch("changeCommunityMask",!0),t.dispatch("changeCommunityClosedRenYuan",!0)}function C(){t.dispatch("changeCommunityMask",!1),t.dispatch("changeCommunityClosedRenYuan",!1)}function E(s){t.dispatch("changeCommunityMask",!0),t.dispatch("changeCommunityClosedStudent",!0),t.dispatch("changeCommunityUpdateStudent",!0),focusStudent.value=s}function Y(){t.dispatch("changeCommunityMask",!1),t.dispatch("changeCommunityClosedStudent",!1),t.dispatch("changeCommunityUpdateStudent",!1)}function N(s){t.dispatch("changeCommunityMask",!0),t.dispatch("changeCommunityClosedSolier",!0),t.dispatch("changeCommunityUpdateSolier",!0),o.focusSolier=s}function W(){t.dispatch("changeCommunityMask",!1),t.dispatch("changeCommunityClosedSolier",!1),t.dispatch("changeCommunityUpdateSolier",!1)}return G(()=>{}),(s,n)=>(m(),v(B,null,[e("div",{class:"goback",onClick:n[0]||(n[0]=Ve=>s.$router.go(-1)),style:{"font-size":"12px"}},n[1]||(n[1]=[e("span",{class:"backText"}," 返回",-1)])),e("div",{class:"qiu",onClick:O},[S(e("img",Se,null,512),[[k,s.$store.state.show.zhan]]),S(e("img",ke,null,512),[[k,!s.$store.state.show.zhan]])]),e("div",$e,D(o.region_name),1),e("iframe",{src:A.value,frameborder:"0",class:"map"},null,8,we),S(e("div",De,[e("div",Ae,[n[2]||(n[2]=e("div",{class:"title"},[e("span",null,"小区概况")],-1)),e("div",He,[_(_e,{dataSex:o.dataSex,dataFace:o.dataFace,dataType:o.dataType,dataHuJi:o.dataHuJi,householder:o.householder,user_sum:o.user_sum,unit:o.unit,building:o.building,onDialogHu:u,onDialog2:r,onDialogDong:c,onDialogHuJi:b,onDialogRenYuan:f},null,8,["dataSex","dataFace","dataType","dataHuJi","householder","user_sum","unit","building"])])])],512),[[k,!s.$store.state.show.zhan]]),S(e("div",xe,[e("div",ze,[n[3]||(n[3]=e("div",{class:"title"},[e("span",null,"年龄结构")],-1)),e("div",Me,[_(le,{dataAge:o.dataAge,subObj:o.subObj,onDialogAge:p},null,8,["dataAge","subObj"])])]),e("div",Le,[n[4]||(n[4]=e("div",{class:"title"},[e("span",null,"人员类别")],-1)),e("div",Fe,[_(re,{dataStudent:o.dataStudent,dataSolier:o.dataSolier,dataPerson:o.dataPerson,dataImportant:o.dataImportant,dataSpecial:o.dataSpecial,onDialog:R,onDialogStudent:E,onDialogSolier:N},null,8,["dataStudent","dataSolier","dataPerson","dataImportant","dataSpecial"])])])],512),[[k,!s.$store.state.show.zhan]]),S(e("div",Je,[n[5]||(n[5]=e("div",{class:"title1"},[e("span",null,"民意互动情况统计")],-1)),e("div",Oe,[_(Q,{dataInteraction:o.dataInteraction},null,8,["dataInteraction"])])],512),[[k,!s.$store.state.show.zhan]]),S(e("div",Re,null,512),[[k,s.$store.state.community.isClosed]]),S(e("div",Te,[s.$store.state.community.no===1?(m(),w(ee,{key:0,onClose:h,type:"3",code:s.$route.query.code},null,8,["code"])):g("",!0),s.$store.state.community.no===2?(m(),w(te,{key:1,onClose:h,type:"3",code:s.$route.query.code},null,8,["code"])):g("",!0),s.$store.state.community.no===3?(m(),w(ae,{key:2,onClose:h,type:"3",code:s.$route.query.code},null,8,["code"])):g("",!0)],512),[[k,s.$store.state.community.isClosed1]]),s.$store.state.community.isClosedAge?(m(),v("div",Ie,[s.$store.state.community.updateData?(m(),w(oe,{key:0,onCloseAge:a,focusAge:o.focusAge||s.$store.state.community.age},null,8,["focusAge"])):g("",!0)])):g("",!0),s.$store.state.community.isClosedHu?(m(),v("div",je,[_(ie,{onCloseHu:y})])):g("",!0),s.$store.state.community.isClosed2?(m(),v("div",Be,[_(ce,{onClose2:d})])):g("",!0),s.$store.state.community.isClosedHuJi?(m(),v("div",Ee,[_(me,{onCloseHuJi:$})])):g("",!0),s.$store.state.community.isClosedRenYuan?(m(),v("div",Ye,[_(fe,{onCloseRenYuan:C})])):g("",!0),s.$store.state.community.isClosedStudent?(m(),v("div",Ne,[K(D(o.focusStudent+"-----"+s.$store.state.community.student+"----"+o.focusStudent||s.$store.state.community.student)+" ",1),s.$store.state.community.updateStudent?(m(),w(de,{key:0,onCloseStudent:Y,focusStudent:s.$store.state.community.student},null,8,["focusStudent"])):g("",!0)])):g("",!0),s.$store.state.community.isClosedSolier?(m(),v("div",We,[s.$store.state.community.updateSolier?(m(),w(ue,{key:0,onCloseSolier:W,focusSolier:s.$store.state.community.solier},null,8,["focusSolier"])):g("",!0)])):g("",!0)],64))}},Ke=I(Ue,[["__scopeId","data-v-84d97266"]]);export{Ke as default};
