import{_ as M,s as D,u as T,r as h,f as E,o as b,g as _,b as k,c as H}from"./index-e6e213aa.js";const S={id:"cesiumContainer"},L={__name:"cesiumB",setup(A){D(),T();let v=h(null);h(!0);let a=h(null),f=E({data:[{lng:105.81498,lat:32.447972,infoId:1015,text:"西城国际小区"},{lng:105.81384,lat:32.448879,infoId:1019,text:"则天新景小区"},{lng:105.814209,lat:32.449343,infoId:1010,text:"供销社住宿楼"},{lng:105.815363,lat:32.450423,infoId:1011,text:"海晟名苑小区"},{lng:105.816278,lat:32.450503,infoId:1018,text:"邮政公寓小区"},{lng:105.815239,lat:32.448782,infoId:1013,text:"农行小区"},{lng:105.816732,lat:32.449639,infoId:1016,text:"凰城·西江月小区"},{lng:105.817044,lat:32.450206,infoId:1012,text:"西岭居小区"},{lng:105.81758,lat:32.449952,infoId:1017,text:"新世纪小区"},{lng:105.815574,lat:32.449697,infoId:1014,text:"税务小区"}],list:[{value1:2,value2:"网格数(个)"},{value1:34,value2:"小区(个)"},{value1:2468,value2:"户数(户)"},{value1:5623,value2:"人数(人)"}]});b(()=>{Cesium.Ion.defaultAccessToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.oeQSme1YSkYiiXbr26yD0qSVT1yTIAwNLjDOkcCGg7M",a.value=new Cesium.Viewer("cesiumContainer",{geocoder:!1,sceneModePicker:!1,navigationHelpButton:!1,baseLayerPicker:!0,homeButton:!1,fullscreenButton:!1,timeline:!1,animation:!1,shouldAnimate:!0,infoBox:!1,selectionIndicator:!1,baseLayerPicker:!1,terrainProvider:new Cesium.EllipsoidTerrainProvider}),a.value.cesiumWidget.creditContainer.style.display="none",a.value.camera.setView({destination:Cesium.Cartesian3.fromDegrees(105.81939353941164,32.**************,600),orientation:{heading:Cesium.Math.toRadians(333.5026502281965),pitch:Cesium.Math.toRadians(-12.883909544389196),roll:.0006212209214681668}});try{const t=new Cesium.Cesium3DTileset({url:"https://cdn.zn.nextv.show/zn/tileset.json"});t.readyPromise.then(()=>{console.log("3D Tileset loaded successfully")}).catch(l=>{console.warn("3D Tileset failed to load:",l),a.value&&a.value.scene&&a.value.scene.primitives.remove(t)}),a.value.scene.primitives.add(t)}catch(t){console.warn("Failed to create 3D Tileset:",t)}a.value.scene.globe.depthTestAgainstTerrain=!0,f.data.map((t,l)=>{a.value.entities.add({id:`area${l}`,infoId:t.infoId,position:new Cesium.Cartesian3.fromDegrees(t.lng,t.lat,500),billboard:{image:"/zuobiao.png",scale:.5,pixelOffset:new Cesium.Cartesian2(0,20)},label:{text:t.text.split("").join(`
`),font:"500 30px Helvetica",scale:.5,style:Cesium.LabelStyle.FILL,fillColor:Cesium.Color.WHITE,showBackground:!0,backgroundColor:new Cesium.Color(228,76,76,1),horizontalOrigin:Cesium.HorizontalOrigin.CENTER,verticalOrigin:Cesium.VerticalOrigin.BASELINE}})}),w(),new Cesium.ScreenSpaceEventHandler(a.value.scene.canvas).setInputAction(function(t){x();let l=a.value.camera.getPickRay(t.position),o=a.value.scene.globe.pick(l,a.value.scene),n=Cesium.Cartographic.fromCartesian(o),s=Cesium.Math.toDegrees(n.longitude),c=Cesium.Math.toDegrees(n.latitude),u=n.height,r={longitude:Number(s.toFixed(6)),latitude:Number(c.toFixed(6)),altitude:Number(u.toFixed(2))};console.log(r)},Cesium.ScreenSpaceEventType.LEFT_CLICK),[[105.818,32.446155,4e3],[105.827,32.456,4e3],[105.83,32.456,4e3],[105.8234,32.449,4e3]].forEach(t=>{var l=I({color:new Cesium.Color(.20784313725490197,.8117647058823529,.3411764705882353,.5),show:!0,positions:[105.813239,32.449161,10,105.815061,32.447548,10,105.81871,32.450701,10,105.817079,32.452158,10,105.813239,32.449161,10],wallHeight:500,hasHeight:!0});l.tag="areaDsLines",window.shiningWalls||(window.shiningWalls=[]),window.shiningWalls.push(l)})});function w(){v.value=new Cesium.ScreenSpaceEventHandler(a.value.scene.canvas),v.value.setInputAction(function(e){let i=a.value.scene.pick(e.position);i&&i.id&&a.value.entities.values.map(t=>{i.id.id&&t.id==i.id.id&&t.infoId&&window.parent.postMessage({type:"b",value:t.infoId,name:t.label.text._value},"*")})},Cesium.ScreenSpaceEventType.LEFT_CLICK),a.value.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)}function x(){var e={},i=a.value.scene,t=i.globe.ellipsoid,l=i.canvas,o=a.value.camera.pickEllipsoid(new Cesium.Cartesian2(0,0),t),n=a.value.camera.pickEllipsoid(new Cesium.Cartesian2(l.width,l.height),t);if(o&&n){var s=t.cartesianToCartographic(o),c=t.cartesianToCartographic(n);e.xmin=Cesium.Math.toDegrees(s.longitude),e.ymax=Cesium.Math.toDegrees(s.latitude),e.xmax=Cesium.Math.toDegrees(c.longitude),e.ymin=Cesium.Math.toDegrees(c.latitude)}else if(!o&&n){var u=null,r=0;do r<=l.height?r+=10:l.height,u=a.value.camera.pickEllipsoid(new Cesium.Cartesian2(0,r),t);while(!u);var d=t.cartesianToCartographic(u),m=t.cartesianToCartographic(n);e.xmax=Cesium.Math.toDegrees(d.longitude),e.ymax=Cesium.Math.toDegrees(d.latitude),e.xmin=Cesium.Math.toDegrees(m.longitude),e.ymin=Cesium.Math.toDegrees(m.latitude)}return e.height=Math.ceil(a.value.camera.positionCartographic.height),e.lon=Cesium.Math.toDegrees(a.value.camera.positionCartographic.longitude),e.lat=Cesium.Math.toDegrees(a.value.camera.positionCartographic.latitude),e.heading=Cesium.Math.toDegrees(a.value.camera.heading),e.pitch=Cesium.Math.toDegrees(a.value.camera.pitch),e.roll=Cesium.Math.toDegrees(a.value.camera.roll),console.log("{'lon':"+e.lon+",'lat':"+e.lat+",'height':"+e.height+",'heading':"+e.heading+",'pitch':"+e.pitch+",'roll':"+e.roll+"}"),e}function I(e){var i=e.color?e.color:Cesium.Color.RED,t=e.maxHeight?e.maxHeight:10,l=e.minHeight?e.minHeight:1,o=[],n=[],s=document.createElement("canvas");s.width=50,s.height=50;var c=s.getContext("2d"),u=c.createLinearGradient(0,20,0,0);u.addColorStop(0,"rgba("+i.red*255+","+i.green*255+","+i.blue*255+",1)"),u.addColorStop(1,"rgba("+i.red*255+","+i.green*255+","+i.blue*255+",0)"),c.fillStyle=u,c.fillRect(0,0,50,50);var r=null;if(e.hasHeight){var d=[];e.positions.forEach((g,p)=>{p%3==2?(n.push(g),o.push(g+(isNaN(e.wallHeight)?1:e.wallHeight))):d.push(g)}),r={wall:{show:e.show!=!1,positions:Cesium.Cartesian3.fromDegreesArray(d),material:new Cesium.ImageMaterialProperty({image:s,transparent:!0}),zIndex:1e3}}}else{for(var m=0;m<e.positions.length/2;m++)n.push(l),o.push(t);r={wall:{show:e.show!=!1,positions:Cesium.Cartesian3.fromDegreesArray(e.positions),material:new Cesium.ImageMaterialProperty({image:s,transparent:!0}),zIndex:1e3}}}r.wall.maximumHeights=o,r.wall.minimumHeights=n;var C=a.value.entities.add(r);return C.wall.material.color=new Cesium.CallbackProperty(function(g,p){var y=.5*Math.abs(Math.sin(new Date().getTime()/500))+.1;return i.withAlpha(y)},!1),C}return _(()=>{a.value.destroy(),a.value=null}),(e,i)=>(k(),H("div",S))}},N=M(L,[["__scopeId","data-v-567299a4"]]);export{N as default};
