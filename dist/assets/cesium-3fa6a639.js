import{_ as b,s as D,u as E,r as v,f as H,o as k,g as I,b as _,c as A}from"./index-e6e213aa.js";const S="/sanjiao01.png",L="/sanjiao02.png";const N={id:"cesiumContainer"},B={__name:"cesium",setup(P){D(),E();let p=v(null),e=v(null),m=v(null),y=H({data:[{lng:105.812686,lat:32.447056,infoId:1,text:"A街区网格",color:"#a460f5"},{lng:105.816116,lat:32.4502,infoId:2,text:"B街区网格",color:"#f66501"}],list:[{value1:2,value2:"网格数(个)"},{value1:34,value2:"小区(个)"},{value1:2468,value2:"户数(户)"},{value1:5623,value2:"人数(人)"}],dataArr:[{name:"女",num:2727},{name:"男",num:2856}]});k(()=>{Cesium.Ion.defaultAccessToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.oeQSme1YSkYiiXbr26yD0qSVT1yTIAwNLjDOkcCGg7M",e.value=new Cesium.Viewer("cesiumContainer",{geocoder:!1,sceneModePicker:!1,navigationHelpButton:!1,baseLayerPicker:!0,homeButton:!1,fullscreenButton:!1,timeline:!1,animation:!1,shouldAnimate:!0,infoBox:!1,selectionIndicator:!1,baseLayerPicker:!1,terrainProvider:new Cesium.EllipsoidTerrainProvider}),e.value.cesiumWidget.creditContainer.style.display="none";try{const n=new Cesium.Cesium3DTileset({url:"https://cdn.zn.nextv.show/zn/tileset.json"});n.readyPromise.then(()=>{console.log("3D Tileset loaded successfully"),e.value&&e.value.zoomTo(n)}).catch(i=>{console.warn("3D Tileset failed to load:",i),e.value&&e.value.scene&&e.value.scene.primitives.remove(n)}),e.value.scene.primitives.add(n),e.value.scene.globe.depthTestAgainstTerrain=!0}catch(n){console.warn("Failed to create 3D Tileset:",n)}y.data.map((n,i)=>{let a=document.createElement("div");a.setAttribute("id",i),a.style.position="absolute",a.style.width="240px",a.style.height="240px",a.style.textAlign="center",a.style.fontSize="30px",a.style.color="white",a.style.fontWeight="bold",a.style.lineHeight="50px",a.style.cursor="pointer";let l=i==0?`
		<div style="background:linear-gradient(to top,#2da8f5,#60d8d3);border-radius:10px">望凤街网格</div>
    <img src="${S}" alt="" id="img1">
    `:`
		<div style="background:linear-gradient(to top, #7def47, #36d057);border-radius:10px">中兴街网格</div>
    <img src="${L}" alt="" id="img2">
    `;a.innerHTML=l,e.value.cesiumWidget.container.appendChild(a);let u=Cesium.Cartesian3.fromDegrees(n.lng,n.lat,500);m.value=()=>{if(!(!e.value||!e.value.scene||!e.value.scene.canvas||!a))try{const s=e.value.scene.canvas.height,r=Cesium.SceneTransforms.wgs84ToWindowCoordinates?Cesium.SceneTransforms.wgs84ToWindowCoordinates(e.value.scene,u):new Cesium.Cartesian2(s/2,s/2);if(r&&a.parentNode){a.style.bottom=s-r.y+"px";const d=a.offsetWidth;a.style.left=r.x-d/2+"px"}}catch(s){console.warn("Cesium coordinate transformation failed:",s),a&&a.parentNode&&(a.style.bottom="50%",a.style.left="50%",a.style.transform="translateX(-50%)")}},e.value.scene.postRender.addEventListener(m.value),a.addEventListener("click",function(){x(i)})}),document.getElementById("img1").animate([{transform:"translateY(20px)"}],{duration:1e3,easing:"linear",iterations:"Infinity",delay:0}),document.getElementById("img2").animate([{transform:"translateY(20px)"}],{duration:1e3,easing:"linear",iterations:"Infinity",delay:0}),p.value=new Cesium.ScreenSpaceEventHandler(e.value.scene.canvas),p.value.setInputAction(function(n){var i=e.value.scene.globe.ellipsoid,a=e.value.camera.pickEllipsoid(n.endPosition,i);if(a){var l=e.value.scene.globe.ellipsoid.cartesianToCartographic(a);Cesium.Math.toDegrees(l.latitude).toFixed(4),Cesium.Math.toDegrees(l.longitude).toFixed(4);var u=(e.value.camera.positionCartographic.height/1e3).toFixed(2);u>=1.5?Array.from(document.getElementsByTagName("div")).map(s=>{(s.id=="0"||s.id=="1")&&(s.style.display="none")}):Array.from(document.getElementsByTagName("div")).map(s=>{(s.id=="0"||s.id=="1")&&(s.style.display="block")})}},Cesium.ScreenSpaceEventType.MOUSE_MOVE),new Cesium.ScreenSpaceEventHandler(e.value.scene.canvas).setInputAction(function(n){M();let i=e.value.camera.getPickRay(n.position),a=e.value.scene.globe.pick(i,e.value.scene),l=Cesium.Cartographic.fromCartesian(a),u=Cesium.Math.toDegrees(l.longitude),s=Cesium.Math.toDegrees(l.latitude),r=l.height,d={longitude:Number(u.toFixed(6)),latitude:Number(s.toFixed(6)),altitude:Number(r.toFixed(2))};console.log(d)},Cesium.ScreenSpaceEventType.LEFT_CLICK),[[105.818,32.446155,4e3],[105.827,32.456,4e3],[105.83,32.456,4e3],[105.8234,32.449,4e3]].forEach(n=>{var i=f({color:new Cesium.Color(.16470588235294117,.6470588235294118,.9686274509803922,.5),show:!0,positions:[105.809825,32.446426,10,105.813169,32.44911,10,105.815061,32.447432,10,105.813891,32.446415,10,105.811862,32.442634,10,105.811501,32.442315,10,105.809825,32.446426,10],wallHeight:500,hasHeight:!0});i.tag="areaDsLines",window.shiningWalls||(window.shiningWalls=[]),window.shiningWalls.push(i)}),[[105.818,32.446155,4e3],[105.827,32.456,4e3],[105.83,32.456,4e3],[105.8234,32.449,4e3]].forEach(n=>{var i=f({color:new Cesium.Color(.20784313725490197,.8117647058823529,.3411764705882353,.5),show:!0,positions:[105.813239,32.449161,10,105.815061,32.447548,10,105.81871,32.450701,10,105.817079,32.452158,10,105.813239,32.449161,10],wallHeight:500,hasHeight:!0});i.tag="areaDsLines",window.shiningWalls||(window.shiningWalls=[]),window.shiningWalls.push(i)})});function x(t){t==0?window.parent.postMessage({type:"wangge",value:"a"},"*"):window.parent.postMessage({type:"wangge",value:"b"},"*")}function f(t){var o=t.color?t.color:Cesium.Color.RED,c=t.maxHeight?t.maxHeight:10,n=t.minHeight?t.minHeight:1,i=[],a=[],l=document.createElement("canvas");l.width=50,l.height=50;var u=l.getContext("2d"),s=u.createLinearGradient(0,20,0,0);s.addColorStop(0,"rgba("+o.red*255+","+o.green*255+","+o.blue*255+",1)"),s.addColorStop(1,"rgba("+o.red*255+","+o.green*255+","+o.blue*255+",0)"),u.fillStyle=s,u.fillRect(0,0,50,50);var r=null;if(t.hasHeight){var d=[];t.positions.forEach((h,w)=>{w%3==2?(a.push(h),i.push(h+(isNaN(t.wallHeight)?1:t.wallHeight))):d.push(h)}),r={wall:{show:t.show!=!1,positions:Cesium.Cartesian3.fromDegreesArray(d),material:new Cesium.ImageMaterialProperty({image:l,transparent:!0}),zIndex:1e3}}}else{for(var g=0;g<t.positions.length/2;g++)a.push(n),i.push(c);r={wall:{show:t.show!=!1,positions:Cesium.Cartesian3.fromDegreesArray(t.positions),material:new Cesium.ImageMaterialProperty({image:l,transparent:!0}),zIndex:1e3}}}r.wall.maximumHeights=i,r.wall.minimumHeights=a;var C=e.value.entities.add(r);return C.wall.material.color=new Cesium.CallbackProperty(function(h,w){var T=.5*Math.abs(Math.sin(new Date().getTime()/500))+.1;return o.withAlpha(T)},!1),C}function M(){var t={},o=e.value.scene,c=o.globe.ellipsoid,n=o.canvas,i=e.value.camera.pickEllipsoid(new Cesium.Cartesian2(0,0),c),a=e.value.camera.pickEllipsoid(new Cesium.Cartesian2(n.width,n.height),c);if(i&&a){var l=c.cartesianToCartographic(i),u=c.cartesianToCartographic(a);t.xmin=Cesium.Math.toDegrees(l.longitude),t.ymax=Cesium.Math.toDegrees(l.latitude),t.xmax=Cesium.Math.toDegrees(u.longitude),t.ymin=Cesium.Math.toDegrees(u.latitude)}else if(!i&&a){var s=null,r=0;do r<=n.height?r+=10:n.height,s=e.value.camera.pickEllipsoid(new Cesium.Cartesian2(0,r),c);while(!s);var d=c.cartesianToCartographic(s),g=c.cartesianToCartographic(a);t.xmax=Cesium.Math.toDegrees(d.longitude),t.ymax=Cesium.Math.toDegrees(d.latitude),t.xmin=Cesium.Math.toDegrees(g.longitude),t.ymin=Cesium.Math.toDegrees(g.latitude)}return t.height=Math.ceil(e.value.camera.positionCartographic.height),t.lon=Cesium.Math.toDegrees(e.value.camera.positionCartographic.longitude),t.lat=Cesium.Math.toDegrees(e.value.camera.positionCartographic.latitude),t.heading=Cesium.Math.toDegrees(e.value.camera.heading),t.pitch=Cesium.Math.toDegrees(e.value.camera.pitch),t.roll=Cesium.Math.toDegrees(e.value.camera.roll),t}return I(()=>{e.value&&e.value.scene&&m.value&&(e.value.scene.postRender.removeEventListener(m.value),m.value=null),e.value&&(e.value.destroy(),e.value=null)}),(t,o)=>(_(),A("div",N))}},z=b(B,[["__scopeId","data-v-2f6b15da"]]);export{z as default};
