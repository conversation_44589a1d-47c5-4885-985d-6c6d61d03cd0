import{_ as R,j as re,r as n,o as V,k as ae,b as g,c as m,d as e,n as W,h as l,t as i,w as x,e as D,l as P,F as j,m as E,p as M,q as oe,v as le,s as ne,g as ie,u as ue,f as ce,E as pe,i as B,x as K}from"./index-e6e213aa.js";import{_ as de,a as Y,b as Z,g as ge,c as me,d as fe,f as ve,e as se,h as he,i as U,j as X,k as _e,s as be,l as ye,m as Ce,n as ke,o as $e,p as we,t as xe,q as Ae,r as Se}from"./di02-e3875920.js";import{b as De,a as ze,c as Ie,t as Ne}from"./bussinessCase-cf5b2069.js";const Me="/more.png";const Pe={class:"dialog"},Le={class:"tabs"},Oe={class:"tab"},Te={src:Z,alt:""},Be={src:Z,alt:""},qe={src:Z,alt:""},He={border:"0"},Qe={key:0,class:"tb1"},Fe={key:1},je={class:"pagination-block"},Re={class:"searchBox"},Ee={class:"ipt"},Ve={style:{"margin-top":"10px"}},Ke={class:"boxs"},Ue={class:"options"},We=["onClick"],Ze={__name:"allPeople",props:{dataFrom:Array,dataType:Array,dataSex:Array,dataFace:Array,user_sum:Number},emits:["close2"],setup(O,{emit:d}){const p=d,h=O,o=()=>{console.log("触发子组件点击"),p("close2")};re();let s=n(1),u=n(!1),A=n([]),z=n([{name:"群众"},{name:"中共党员"},{name:"全部"}]),I=n(""),k=n("全部"),N=n(!1);n([]);let T=n(0),H=n(0),Q=n(0),L=n(0),b=n(1),c=n(1),y=n(15);function f(C){s.value=C,c.value=1,k.value="全部",F()}function S(){u.value=!0}function w(){c.value=1,setTimeout(()=>{F(),q()},300)}function q(){u.value=!1,I.value=""}function a(){N.value=!N.value}function _(C){k.value=C.name,N.value=!N.value}function J(C){c.value=C,F()}function G(){ge({type:"1",code:""}).then(C=>{T.value=C.data.huJiRenKou,H.value=C.data.waiLaiRenKou})}function ee(){me({type:"1",code:""}).then(C=>{L.value=C.data.liuDongRenKou,Q.value=C.data.changZhuRenKou})}function F(){console.log(k.value),fe({personnel_type:s.value,limit:15,offset:(c.value/1-1)*15,name:I.value,political_outlook:k.value=="全部"?"":k.value}).then(C=>{b.value=C.data.count,A.value=C.data.rows})}return V(()=>{G(),ee(),F(),console.log(h)}),(C,t)=>{const r=ae("el-pagination");return g(),m("div",Pe,[t[18]||(t[18]=e("div",{class:"title"},[e("span",null,"总人数( 7394 人)")],-1)),e("div",Le,[e("div",Oe,[e("div",{class:"single",onClick:t[0]||(t[0]=v=>f(2))},[e("div",{style:W(l(s)==2?{"font-weight":"bold"}:{})},"流动人口("+i(l(L))+"人)",5),x(e("img",Te,null,512),[[D,l(s)==2]])]),e("div",{class:"single",onClick:t[1]||(t[1]=v=>f(3))},[e("div",{style:W(l(s)==3?{"font-weight":"bold"}:{})},"户籍人口("+i(l(T))+"人)",5),x(e("img",Be,null,512),[[D,l(s)==3]])]),e("div",{class:"single",onClick:t[2]||(t[2]=v=>f(4))},[e("div",{style:W(l(s)==4?{"font-weight":"bold"}:{})},"常住人口("+i(l(Q))+"人)",5),x(e("img",qe,null,512),[[D,l(s)==4]])])]),e("div",{class:"close",onClick:o},t[4]||(t[4]=[e("img",{src:de,alt:""},null,-1),e("span",null," 返回",-1)]))]),e("table",He,[e("thead",null,[t[7]||(t[7]=e("th",null,"序号",-1)),e("th",{onClick:S},t[5]||(t[5]=[P(" 姓名 ",-1),e("img",{src:Y,class:"searchs"},null,-1)])),t[8]||(t[8]=e("th",null,"身份证号码",-1)),t[9]||(t[9]=e("th",null,"性别",-1)),t[10]||(t[10]=e("th",null,"电话",-1)),t[11]||(t[11]=e("th",null,"家庭关系",-1)),t[12]||(t[12]=e("th",null,"小区",-1)),t[13]||(t[13]=e("th",null,"住址",-1)),t[14]||(t[14]=e("th",null,"职业",-1)),e("th",{onClick:S},t[6]||(t[6]=[P("政治面貌",-1),e("img",{src:Y,class:"searchs"},null,-1)]))]),l(A).length?(g(),m("tbody",Qe,[(g(!0),m(j,null,E(l(A),(v,$)=>(g(),m("tr",{key:v.id},[e("td",null,i($+1),1),e("td",null,i(v.name),1),e("td",null,i(v.id_card),1),e("td",null,i(v.gender),1),e("td",null,i(v.phone),1),e("td",null,i(v.family_relation),1),e("td",null,i(v.address),1),e("td",null,i(v.room),1),e("td",null,i(v.occupation),1),e("td",null,i(v.political?v.political:"群众"),1)]))),128))])):(g(),m("tbody",Fe,t[15]||(t[15]=[e("p",{class:"zan"},"暂无数据！",-1)])))]),e("div",je,[M(r,{layout:"prev, pager, next",total:l(b),style:{"--el-pagination-font-size":"16px","--el-pagination-button-disabled-bg-color":"none","--el-pagination-bg-color":"none","--el-pagination-button-color":"white"},"current-page":l(c),"page-size":l(y),onCurrentChange:J},null,8,["total","current-page","page-size"])]),x(e("div",Re,[e("div",Ee,[e("div",null,[t[16]||(t[16]=e("div",{class:"label"},"姓  名",-1)),x(e("input",{type:"text","onUpdate:modelValue":t[3]||(t[3]=v=>oe(I)?I.value=v:I=v),placeholder:"请输入姓名",class:"ipt1"},null,512),[[le,l(I)]])]),e("div",Ve,[e("div",Ke,[t[17]||(t[17]=e("div",{class:"label"},"政治面貌",-1)),e("div",{class:"selects",onClick:a},i(l(k)),1)]),x(e("div",Ue,[(g(!0),m(j,null,E(l(z),v=>(g(),m("div",{onClick:$=>_(v)},i(v.name),9,We))),256))],512),[[D,l(N)]])])]),e("div",{class:"btns"},[e("div",{onClick:w},"搜索"),e("div",{onClick:q},"取消")])],512),[[D,l(u)]])])}}},Je=R(Ze,[["__scopeId","data-v-721f029a"]]);const Xe={class:"dialog3"},Ye={class:"tabs3"},Ge={class:"tab3"},et={src:Z,alt:""},tt={src:Z,alt:""},st={border:"0"},at={key:0,class:"tb1"},ot={key:1},lt={class:"pagination-block"},nt={class:"searchBox"},it={class:"ipt"},dt={style:{"margin-top":"10px"}},rt={class:"boxs"},ut={class:"label"},ct={class:"options"},pt=["onClick"],gt={__name:"resident",props:{dataStudent:Array,dataSolier:Array},emits:["close3"],setup(O,{emit:d}){const p=d,h=()=>{p("close3")};let o=n(1),s=n([]),u=n(0),A=n(0),z=n(""),I=n(!1),k=n(!1),N=n("全部"),T=n([]),H=n([{name:"小学",value:1},{name:"初中",value:2},{name:"高中",value:3},{name:"大学",value:4},{name:"全部"}]),Q=n([{name:"社区矫正人员",value:1},{name:"刑满释放人员",value:2},{name:"稳控人员",value:3},{name:"吸毒人员",value:4},{name:"全部"}]),L=n(0),b=n(0),c=n(1),y=n(1),f=n(15);function S(t){o.value=t,N.value="全部",t==1?(C(),T.value=H.value):(F(),T.value=Q.value)}function w(){I.value=!0}function q(){y.value=1,o.value==1?setTimeout(()=>{C(),a()},100):setTimeout(()=>{F(),a()},100)}function a(){I.value=!1,z.value=""}function _(){k.value=!k.value}function J(t){N.value=t.name,k.value=!k.value,o.value==1?L.value=t.value:b.value=t.value}function G(t){y.value=t,o.value==1?C():F()}function ee(t){let r=[];for(let v in t)Array.isArray(t[v])&&(r=r.concat(t[v]));return r}function F(){he({limit:15,offset:(y.value/1-1)*15,type:"1",code:"",name:z.value,focus_type:b.value?b.value:""}).then(t=>{c.value=t.data.count,s.value=t.data.rows})}function C(t){se({limit:15,offset:(y.value/1-1)*15,name:z.value,type:"1",code:"",student_type:L.value?L.value:""}).then(r=>{c.value=r.data.count,s.value=r.data.rows})}return V(()=>{ve({type:"1",code:""}).then(t=>{let r=ee(t.data);A.value=r.length}),C(),T.value=H.value,se({limit:15,offset:(y.value/1-1)*15,name:z.value,type:"1",code:"",student_type:L.value?L.value:""}).then(t=>{u.value=t.data.count})}),(t,r)=>{const v=ae("el-pagination");return g(),m("div",Xe,[r[13]||(r[13]=e("div",{class:"title3"},[e("span",null,"社会人员")],-1)),e("div",Ye,[e("div",Ge,[e("div",{class:"single3",onClick:r[0]||(r[0]=$=>S(1))},[e("div",{style:W(l(o)==1?{"font-weight":"bold"}:{})},"学生构成("+i(l(u))+"人)",5),x(e("img",et,null,512),[[D,l(o)==1]])]),e("div",{class:"single3",onClick:r[1]||(r[1]=$=>S(2))},[e("div",{style:W(l(o)==2?{"font-weight":"bold"}:{})},"重点监管人群("+i(l(A))+"人)",5),x(e("img",tt,null,512),[[D,l(o)==2]])])]),e("div",{class:"close3",onClick:h},r[3]||(r[3]=[e("img",{src:de,alt:""},null,-1),e("span",null," 返回",-1)]))]),e("table",st,[e("thead",null,[r[6]||(r[6]=e("th",null,"序号",-1)),e("th",{onClick:w},r[4]||(r[4]=[P(" 姓名 ",-1),e("img",{src:Y,class:"searchs"},null,-1)])),r[7]||(r[7]=e("th",null,"性别",-1)),r[8]||(r[8]=e("th",null,"小区",-1)),r[9]||(r[9]=e("th",null,"住址",-1)),r[10]||(r[10]=e("th",null,"电话",-1)),e("th",{onClick:w},[P(i(l(o)==1?"学生构成":"类别")+" ",1),r[5]||(r[5]=e("img",{src:Y,class:"searchs"},null,-1))])]),l(s).length?(g(),m("tbody",at,[(g(!0),m(j,null,E(l(s),($,te)=>(g(),m("tr",{key:$.id},[e("td",null,i(te+1),1),e("td",null,i($.name),1),e("td",null,i($.gender),1),e("td",null,i($.address),1),e("td",null,i($.room),1),e("td",null,i($.phone),1),e("td",null,i(l(o)==1?$.student_type:$.focus_type),1)]))),128))])):(g(),m("tbody",ot,r[11]||(r[11]=[e("p",{class:"zan"},"暂无数据！",-1)])))]),e("div",lt,[M(v,{layout:"prev, pager, next",total:l(c),style:{"--el-pagination-font-size":"16px","--el-pagination-button-disabled-bg-color":"none","--el-pagination-bg-color":"none","--el-pagination-button-color":"white"},"current-page":l(y),"page-size":l(f),onCurrentChange:G},null,8,["total","current-page","page-size"])]),x(e("div",nt,[e("div",it,[e("div",null,[r[12]||(r[12]=e("div",{class:"label"},"姓  名",-1)),x(e("input",{type:"text","onUpdate:modelValue":r[2]||(r[2]=$=>oe(z)?z.value=$:z=$),placeholder:"请输入姓名",class:"ipt1"},null,512),[[le,l(z)]])]),e("div",dt,[e("div",rt,[e("div",ut,i(l(o)==1?"学生构成":"重点监管"),1),e("div",{class:"selects",onClick:_},i(l(N)),1)]),x(e("div",ct,[(g(!0),m(j,null,E(l(T),$=>(g(),m("div",{onClick:te=>J($)},i($.name),9,pt))),256))],512),[[D,l(k)]])])]),e("div",{class:"btns"},[e("div",{onClick:q},"搜索"),e("div",{onClick:a},"取消")])],512),[[D,l(I)]])])}}},mt=R(gt,[["__scopeId","data-v-bb0c9a0d"]]),ft="/di03.png";const vt={class:"_box"},ht={class:"single"},_t={class:"top"},bt={class:"text"},yt={class:"text2"},Ct={__name:"serviceCase",props:{dataService:Array},emits:["increase"],setup(O,{emit:d}){const p=O;return V(()=>{}),(h,o)=>(g(),m("div",vt,[(g(!0),m(j,null,E(p.dataService,s=>(g(),m("div",ht,[e("div",_t,[e("span",bt,[P(i(s.sum)+" ",1),o[0]||(o[0]=e("span",{style:{"font-size":"16px"}},"次/年",-1))]),o[1]||(o[1]=e("div",{style:{height:"10px"}},null,-1)),e("span",yt,i(s.name)+"（次）",1)]),o[2]||(o[2]=e("img",{src:ft,alt:""},null,-1))]))),256))]))}},kt=R(Ct,[["__scopeId","data-v-64a4eaae"]]);const $t={class:"_box"},wt={class:"top"},xt={class:"top"},At={class:"top"},St={__name:"streetData",props:{householder:Number,user_sum:Number,xiaoqu:Number,gridNum:Number},emits:["increase"],setup(O,{emit:d}){const p=d,h=O;function o(s){switch(s){case 4:p("dialog2");break;case 3:p("dialogHu");break;case 2:p("dialogCommunity");break;case 0:p("dialogBranch");break}}return(s,u)=>(g(),m("div",$t,[e("div",{class:"single",onClick:u[0]||(u[0]=A=>o(0))},u[5]||(u[5]=[e("div",{class:"top"},i(2),-1),e("img",{src:U,alt:""},null,-1),e("span",{class:"bottom"},"党支部(个)",-1)])),e("div",{class:"single",onClick:u[1]||(u[1]=A=>o(1))},[e("div",wt,i(h.gridNum),1),u[6]||(u[6]=e("img",{src:U,alt:""},null,-1)),u[7]||(u[7]=e("span",{class:"bottom"},"网格数(个)",-1))]),e("div",{class:"single",onClick:u[2]||(u[2]=A=>o(2))},[e("div",xt,i(h.xiaoqu),1),u[8]||(u[8]=e("img",{src:U,alt:""},null,-1)),u[9]||(u[9]=e("span",{class:"bottom"},"小区(个)",-1))]),e("div",{class:"single",onClick:u[3]||(u[3]=A=>o(3))},[e("div",At,i(h.householder),1),u[10]||(u[10]=e("img",{src:U,alt:""},null,-1)),u[11]||(u[11]=e("span",{class:"bottom"},"总户数(户)",-1))]),e("div",{class:"single",onClick:u[4]||(u[4]=A=>o(4))},u[12]||(u[12]=[e("div",{class:"top"},"7394",-1),e("img",{src:U,alt:""},null,-1),e("span",{class:"bottom"},"总人数(人)",-1)]))]))}},Dt=R(St,[["__scopeId","data-v-ad68968c"]]);const zt={class:"subtext"},It={class:"n1"},Nt={class:"per"},Mt={class:"n2"},Pt={class:"per"},Lt={class:"n3"},Ot={class:"per"},Tt={class:"pie"},Bt={__name:"peopleData",props:{dataPerson:Array,dataAge:Array,dataImportant:Array,dataSpecial:Array,subObj:Object},emits:["dialog"],setup(O,{emit:d}){const p=ne(),h=d,o=O,s=n(null),u=n(null),A=n(null),z=n(null);V(()=>{setTimeout(()=>{I(),k(),N(),T()},500)});function I(){const b=X(s.value);var c,y=[],f=[],S=50;o.dataAge.map(function(w,q){y.push(w.name),w.value===0?f.push(w.value+S):f.push(w.value)}),c={title:{text:"年龄构成",left:"center",textStyle:{color:"#fff"},top:-2},backgroundColor:"transparent",color:["#3398DB"],grid:{left:"5%",right:"5%",top:"20%",containLabel:!0},xAxis:[{type:"category",gridIndex:0,data:y,axisTick:{show:!1},axisLine:{lineStyle:{color:"white"}},axisLabel:{show:!0,color:"white",fontSize:14}}],yAxis:{type:"value",splitNumber:4,interval:1e3,splitLine:{show:!1},axisTick:{show:!1},min:0,axisLine:{lineStyle:{color:"white",width:10}},axisLabel:{color:"white",formatter:"{value}"}},series:[{name:"人数",type:"bar",barWidth:"30%",xAxisIndex:0,yAxisIndex:0,itemStyle:{barBorderRadius:[30,30,0,0],color:"#2FC6DC"},showBackground:!0,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)"},data:f,label:{show:!0,position:"top",color:"white"},zlevel:11}]},c&&b.setOption(c),b.getZr().on("click",w=>{let q=[w.offsetX,w.offsetY];if(b.containPixel("grid",q)){switch(b.convertFromPixel({seriesIndex:0},[w.offsetX,w.offsetY])[0]){case 1:h("dialogAge",1),p.dispatch("changeAge",1);break;case 2:h("dialogAge",2),p.dispatch("changeAge",2);break;case 3:h("dialogAge",3),p.dispatch("changeAge",3);break;case 4:h("dialogAge",4),p.dispatch("changeAge",4);break;case 5:h("dialogAge",5),p.dispatch("changeAge",5);break;case 6:h("dialogAge",6),p.dispatch("changeAge",6);break;default:h("dialogAge",0),p.dispatch("changeAge",0);break}p.dispatch("changeMask",!0),p.dispatch("changeClosedAge",!0),p.dispatch("changeClosedUpdate",!0)}})}function k(){const b=X(u.value),c=[[{offset:0,color:"rgba(23, 77, 216, 1)"},{offset:1,color:"rgba(23, 77, 216, 1)"}],[{offset:0,color:"rgba(39, 89, 217, 1)"},{offset:1,color:"rgba(39, 89, 217, 1)"}],[{offset:0,color:"rgba(29, 100, 242, 1)"},{offset:1,color:"rgba(29, 100, 242, 1)"}],[{offset:0,color:"rgba(54, 116, 242, 1)"},{offset:1,color:"rgba(54, 116, 242, 1)"}],[{offset:0,color:"rgba(78, 136, 255, 1)"},{offset:1,color:"rgba(82, 139, 255, 1)"}],[{offset:0,color:"rgba(255, 205, 94, 1)"},{offset:1,color:"rgba(253, 203, 94, 1)"}],[{offset:0,color:"rgba(205, 94, 244, 1)"},{offset:1,color:"rgba(147, 42, 225, 1)"}],[{offset:0,color:"rgba(245, 146, 106, 1)"},{offset:1,color:"rgba(221, 75, 52, 1)"}]],y=o.dataPerson.map((S,w)=>({name:S.name,value:S.num,itemStyle:{color:{type:"linear",colorStops:c[w]}}}));var f={title:{text:"社区名人",left:"center",textStyle:{color:"white"},top:6},tooltip:{trigger:"item",formatter:"{b} : {c} ({d}%)",backgroundColor:"#06111f",textStyle:{color:"white"}},series:[{name:"Area Mode",type:"pie",radius:[30,80],center:["50%","50%"],roseType:"area",label:{position:"inside",textStyle:{color:"white",fontWeight:"bold"},formatter:function(S){return S.value+`
`+S.name}},data:y}]};f&&b.setOption(f)}function N(){const b=X(A.value);var c;const y=o.dataSpecial.map((f,S)=>({name:f.name,value:f.num}));c={backgroundColor:"transparent",title:{text:"特殊人群",textStyle:{color:"#fff"},left:80,top:7},tooltip:{show:!0,trigger:"item",formatter:"{b}: {c} ({d}%)",backgroundColor:"#00060b",textStyle:{color:"white"},borderColor:"#00060b"},series:[{type:"pie",selectedMode:"single",radius:["25%","58%"],z:1,color:["#2759D9","#4E88FF","#1d64f2","#78a4ff","#174dd8"],label:{position:"inner",formatter:`{c}
{b}`,textStyle:{color:"#fff",fontWeight:"bold",fontSize:14}},labelLine:{show:!1},data:y},{type:"pie",radius:["0","30%"],z:0,itemStyle:{color:"rgba(0,0,0,.3)"},data:[0]}]},c&&b.setOption(c)}function T(){const b=X(z.value),c=o.dataImportant.map(f=>({value:f.value,name:f.name}));var y={title:{text:"军人构成",textStyle:{color:"#fff"},left:60,top:7},backgroundColor:"transparent",tooltip:{trigger:"item",backgroundColor:"rgba(0,0,0,0.9)",formatter:function(f){return f.marker+'<span style="color:white">'+f.data.name+`
`+f.data.value+"</span>"}},series:[{name:"",type:"pie",hoverAnimation:!1,legendHoverLink:!1,cursor:"default",right:20,radius:["32%","50%"],center:["50%","50%"],color:["rgba(85, 149, 255,0.2)","rgba(15, 105, 241, 0.2)","rgba(48, 198, 220, 0.2)","rgba(78, 136, 255, 0.2)","rgba(15, 163, 241, 0.2)"],label:{show:!1},labelLine:{show:!1},zlevel:1,itemStyle:{borderColor:"#0a1a2a",ellipsis:{borderColor:"#0a1a2a"}},tooltip:{show:!1},data:c},{name:"",type:"pie",zlevel:2,right:20,cursor:"default",hoverAnimation:!1,legendHoverLink:!1,radius:["37%","50%"],center:["50%","50%"],color:["rgba(85, 149, 255, 0.5)","rgba(15, 105, 241, 0.5)","rgba(48, 198, 220, 0.5)","rgba(78, 136, 255, 0.5)","rgba(15, 163, 241, 0.5)"],label:{show:!1},labelLine:{show:!1},itemStyle:{borderColor:"#0a1a2a",ellipsis:{borderColor:"#0a1a2a"}},tooltip:{show:!1},data:c},{name:"title",type:"pie",zlevel:3,right:20,hoverAnimation:!1,legendHoverLink:!1,radius:["43%","58%"],center:["50%","50%"],color:["rgba(85, 149, 255, 1)","rgba(15, 105, 241, 1)","rgba(48, 198, 220, 1)","rgba(78, 136, 255, 1)","rgba(15, 163, 241, 1)"],label:{show:!0,formatter:f=>`
              ${f.data.value}
              ${f.name}
              `,textStyle:{color:"white",fontWeight:"bold"},position:"inside"},data:c}]};y&&b.setOption(y)}function H(){h("dialog",1),p.dispatch("changeNo",4),p.dispatch("changeMask",!0),p.dispatch("changeClosed1",!0)}function Q(){h("dialog",2),p.dispatch("changeNo",2),p.dispatch("changeMask",!0),p.dispatch("changeClosed1",!0)}function L(){h("dialog",3),p.dispatch("changeNo",3),p.dispatch("changeMask",!0),p.dispatch("changeClosed1",!0)}return ie(()=>{}),(b,c)=>(g(),m(j,null,[e("div",{ref_key:"age",ref:s,id:"age"},null,512),e("div",zt,[c[0]||(c[0]=e("span",{class:"name"},"未成年人：",-1)),e("span",It,i(o.subObj.teenager),1),e("span",Nt,"("+i(o.subObj.teenager_percent)+")",1),c[1]||(c[1]=P("   ",-1)),c[2]||(c[2]=e("span",{class:"name"},"青壮年：",-1)),e("span",Mt,i(o.subObj.middle_aged),1),e("span",Pt,"("+i(o.subObj.middle_aged_percent)+")",1),c[3]||(c[3]=P("   ",-1)),c[4]||(c[4]=e("span",{class:"name"},"老年人：",-1)),e("span",Lt,i(o.subObj.old_aged),1),e("span",Ot,"("+i(o.subObj.old_aged_percent)+")",1)]),e("div",Tt,[e("div",{ref_key:"person",ref:u,id:"person",onClick:H},null,512),e("div",{ref_key:"special",ref:A,id:"special",onClick:Q},null,512),e("div",{ref_key:"important",ref:z,id:"important",onClick:L},null,512)])],64))}},qt=R(Bt,[["__scopeId","data-v-0385fe47"]]),Ht="/zhengdi.png";const Qt={class:"_box"},Ft={class:"single"},jt={class:"top"},Rt={class:"safe"},Et={class:"text1"},Vt={class:"danger"},Kt={class:"text1"},Ut={class:"text1"},Wt={class:"bottom"},Zt={__name:"safeData",props:{safeData:Array},emits:["increase"],setup(O,{emit:d}){const p=O;return V(()=>{}),(h,o)=>(g(),m("div",Qt,[(g(!0),m(j,null,E(p.safeData,s=>(g(),m("div",Ft,[e("div",jt,[e("div",Rt,[o[2]||(o[2]=P(" 排查：",-1)),e("span",Et,[P(i(s.frequency),1),o[0]||(o[0]=e("span",{style:{"font-size":"12px"}},"次",-1)),o[1]||(o[1]=P("/月",-1))])]),e("div",Vt,[o[3]||(o[3]=P(" 隐患：",-1)),e("span",Kt,i(s.value1),1),o[4]||(o[4]=P("个 整改：",-1)),e("span",Ut,i(s.value1),1),o[5]||(o[5]=P("个 ",-1))])]),o[6]||(o[6]=e("img",{src:Ht,alt:""},null,-1)),e("div",Wt,i(s.value2),1)]))),256))]))}},Jt=R(Zt,[["__scopeId","data-v-cf1241e0"]]);const Xt={src:ke,alt:""},Yt={src:$e,alt:""},Gt=["src"],es={key:0,class:"left"},ts={class:"one"},ss={class:"one_dis"},as={class:"two"},os={class:"two_dis"},ls={class:"three"},ns={class:"three_dis"},is={key:1,class:"right"},ds={class:"one"},rs={class:"one_dis"},us={class:"two"},cs={class:"two_dis"},ps={class:"bottom"},gs={class:"bot_dis"},ms={class:"mengban"},fs={class:"dialog"},vs={key:2,class:"dialog"},hs={key:3,class:"dialog"},_s={class:"dialog"},bs={class:"dialog"},ys={key:4,class:"dialog"},Cs={key:5,class:"dialog"},ks={__name:"wangge",setup(O){const d=ne(),p=ue(),h=n("map"),o=n(!0);n(1),n(!1),n(!1),n(!1),n(!1),n(!1),n(!1),n(!1),n(!1),n(!1),n(!1);let s=ce({subObj:{},householder:0,user_sum:0,dataFrom:[],dataType:[],dataSex:[],dataFace:[],safe:[{frequency:25,value1:2,value2:"地质灾害"},{frequency:45,value1:5,value2:"防汛抗旱"},{frequency:15,value1:3,value2:"气安全隐患"}],dataService:[{sum:26,name:"理论教育"},{sum:6,name:"专业教育"},{sum:2,name:"医疗服务"},{sum:4,name:"就业教育"},{sum:3,name:"文化教育"},{sum:10,name:"社会服务"}],dataStudent:[{name:"大学",value:200},{name:"高中",value:150},{name:"初中",value:200},{name:"小学",value:100}],dataAge:[],dataPerson:[],dataSpecial:[],dataImportant:[],dataSolier:[],dataInteraction:{data1:[5,8,2,20,5,10],data2:[10,5,3,10,5,20],data3:[20,6,10,5,20,6],data4:[10,6,20,2,10,6]},focusAge:null,gridNum:0,xiaoqu:0});V(()=>{u(),window.addEventListener("message",A),_e({limit:15,offset:0,type:"1",code:"",householder_name:""}).then(a=>{s.householder=a.data.count})});async function u(){const a=await be({type:"1",code:""});a.code==200?(s.user_sum=a.data.user_sum,s.dataFrom=a.data.domicile_from,s.dataType=a.data.domicile_type,s.dataSex=a.data.gender,s.dataFace=a.data.political,s.dataAge=a.data.age,s.dataPerson=a.data.famous_type,s.dataSpecial=a.data.special_group,s.dataImportant=a.data.soldier_type,s.dataSolier=a.data.soldier_type,s.dataStudent=a.data.student,s.xiaoqu=a.data.xiaoqu,s.gridNum=a.data.gridNum,s.subObj={teenager:a.data.teenager,teenager_percent:a.data.teenager_percent,middle_aged:a.data.middle_aged,middle_aged_percent:a.data.middle_aged_percent,old_aged:a.data.old_aged,old_aged_percent:a.data.old_aged_percent}):pe.error(a.message)}function A(a){a.data.type=="wangge"&&o.value&&(a.data.value=="a"?(o.value=!1,p.replace({path:"/communities",query:{code:"10"}})):(o.value=!1,p.replace({path:"/communities",query:{code:"20"}})))}function z(){d.dispatch("changeZhan")}function I(a){d.state.sheQu.no=a,console.log("closed1",a),d.dispatch("changeMask",!0),d.dispatch("changeClosed1",!0)}function k(){d.dispatch("changeMask",!1),d.dispatch("changeClosed1",!1)}function N(){d.dispatch("changeMask",!0),d.dispatch("changeClosed2",!0)}function T(){d.dispatch("changeMask",!1),d.dispatch("changeClosed2",!1)}function H(){d.dispatch("changeMask",!0),d.dispatch("changeClosedHu",!0)}function Q(){d.dispatch("changeMask",!1),d.dispatch("changeClosedHu",!1)}function L(){d.dispatch("changeMask",!0),d.dispatch("changeClosedCommunity",!0)}function b(){d.dispatch("changeMask",!1),d.dispatch("changeClosedCommunity",!1)}function c(){d.dispatch("changeMask",!0),d.dispatch("changeClosed3",!0)}function y(){d.dispatch("changeMask",!1),d.dispatch("changeClosed3",!1)}function f(){d.dispatch("changeMask",!0),d.dispatch("changeClosedBranch",!0)}function S(){d.dispatch("changeMask",!1),d.dispatch("changeClosedBranch",!1)}function w(a){s.focusAge=a,d.dispatch("changeMask",!0),d.dispatch("changeClosedAge",!0),d.dispatch("changeClosedUpdate",!0)}function q(){d.dispatch("changeMask",!1),d.dispatch("changeClosedAge",!1),d.dispatch("changeClosedUpdate",!1)}return ie(()=>{h.value="map?"+new Date().getTime(),window.removeEventListener("message",A)}),(a,_)=>(g(),m(j,null,[e("div",{class:"goback",onClick:_[0]||(_[0]=J=>a.$router.replace("/home")),style:{"font-size":"12px"}},_[1]||(_[1]=[e("span",{class:"backText"}," 返回",-1)])),e("div",{class:"qiu",onClick:z},[x(e("img",Xt,null,512),[[D,a.$store.state.show.zhan]]),x(e("img",Yt,null,512),[[D,!a.$store.state.show.zhan]])]),_[8]||(_[8]=e("div",{class:"biao"},"上西则南社区",-1)),e("iframe",{src:h.value,frameborder:"0",class:"map"},null,8,Gt),a.$store.state.show.zhan?B("",!0):(g(),m("div",es,[e("div",ts,[_[2]||(_[2]=e("div",{class:"title"},[e("span",null,"社区概况")],-1)),e("div",ss,[M(Dt,{gridNum:l(s).gridNum,xiaoqu:l(s).xiaoqu,householder:l(s).householder,user_sum:l(s).user_sum,onDialog2:N,onDialogHu:H,onDialogCommunity:L,onDialogBranch:f},null,8,["gridNum","xiaoqu","householder","user_sum"])])]),e("div",as,[_[3]||(_[3]=e("div",{class:"title"},[e("span",null,"经济结构")],-1)),e("div",os,[M(De,{onDialogBussiness:I})])]),e("div",ls,[_[4]||(_[4]=e("div",{class:"title"},[e("span",null,"社区安全")],-1)),e("div",ns,[M(Jt,{safeData:l(s).safe},null,8,["safeData"])])])])),a.$store.state.show.zhan?B("",!0):(g(),m("div",is,[e("div",ds,[e("div",{class:"title"},[_[5]||(_[5]=e("span",null,"社区人员",-1)),e("img",{src:Me,alt:"",onClick:c,class:"more"})]),e("div",rs,[M(qt,{dataAge:l(s).dataAge,dataPerson:l(s).dataPerson,dataSpecial:l(s).dataSpecial,dataImportant:l(s).dataImportant,subObj:l(s).subObj,onDialog:I,onDialogAge:w},null,8,["dataAge","dataPerson","dataSpecial","dataImportant","subObj"])])]),e("div",us,[_[6]||(_[6]=e("div",{class:"title"},[e("span",null,"社区服务")],-1)),e("div",cs,[M(kt,{dataService:l(s).dataService},null,8,["dataService"])])])])),x(e("div",ps,[_[7]||(_[7]=e("div",{class:"title1"},[e("span",null,"民意互动情况统计")],-1)),e("div",gs,[M(ye,{dataInteraction:l(s).dataInteraction},null,8,["dataInteraction"])])],512),[[D,!a.$store.state.show.zhan]]),x(e("div",ms,null,512),[[D,a.$store.state.sheQu.isClosed]]),x(e("div",fs,[a.$store.state.sheQu.no===1?(g(),K(xe,{key:0,onClose:k,type:"1",code:""})):B("",!0),a.$store.state.sheQu.no===2?(g(),K(Ae,{key:1,onClose:k,type:"1",code:""})):B("",!0),a.$store.state.sheQu.no===3?(g(),K(Se,{key:2,onClose:k,type:"1",code:""})):B("",!0),a.$store.state.sheQu.no===4?(g(),K(Ne,{key:3,onClose:k,type:"1",code:""})):B("",!0)],512),[[D,a.$store.state.sheQu.isClosed1]]),a.$store.state.sheQu.isClosed2?(g(),m("div",vs,[M(Je,{onClose2:T})])):B("",!0),a.$store.state.sheQu.isClosedHu?(g(),m("div",hs,[M(we,{onCloseHu:Q})])):B("",!0),x(e("div",_s,[M(ze,{onCloseCommunity:b})],512),[[D,a.$store.state.sheQu.isClosedCommunity]]),x(e("div",bs,[M(Ie,{onCloseBranch:S})],512),[[D,a.$store.state.sheQu.isClosedBranch]]),a.$store.state.sheQu.isClosed3?(g(),m("div",ys,[M(mt,{onClose3:y,dataSolier:l(s).dataSolier,dataStudent:l(s).dataStudent},null,8,["dataSolier","dataStudent"])])):B("",!0),a.$store.state.sheQu.isClosedAge?(g(),m("div",Cs,[a.$store.state.sheQu.updateData?(g(),K(Ce,{key:0,onCloseAge:q,focusAge:l(s).focusAge||a.$store.state.sheQu.age},null,8,["focusAge"])):B("",!0)])):B("",!0)],64))}},As=R(ks,[["__scopeId","data-v-579045d7"]]);export{As as default};
