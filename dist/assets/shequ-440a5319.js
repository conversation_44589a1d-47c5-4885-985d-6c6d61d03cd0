import{_ as I,r as h,u as S,f as _,o as A,g as L,b as x,c as M,h as N,d as T,i as P,F as B}from"./index-e6e213aa.js";const F="/dasanjiao.png";const z={__name:"shequ",setup(R){let a=h(null);h(null);let g=h(null);const f=S(),v=h(null);let y=h(!1);_({data:[{lng:105.817043,lat:32.44637,infoId:1},{lng:105.824314,lat:32.4526,infoId:2}]}),A(()=>{y.value=/android/i.test(navigator.userAgent),Cesium.Ion.defaultAccessToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.oeQSme1YSkYiiXbr26yD0qSVT1yTIAwNLjDOkcCGg7M",a.value=new Cesium.Viewer("cesiumContainer",{geocoder:!1,sceneModePicker:!1,navigationHelpButton:!1,baseLayerPicker:!0,homeButton:!1,fullscreenButton:!1,timeline:!1,animation:!1,shouldAnimate:!0,infoBox:!1,selectionIndicator:!1,baseLayerPicker:!1,terrainProvider:new Cesium.EllipsoidTerrainProvider}),a.value.cesiumWidget.creditContainer.style.display="none";try{const e=new Cesium.Cesium3DTileset({url:"https://cdn.zn.nextv.show/zn/tileset.json"});e.readyPromise.then(()=>{console.log("3D Tileset loaded successfully")}).catch(t=>{console.warn("3D Tileset failed to load:",t),a.value&&a.value.scene&&a.value.scene.primitives.remove(e)}),a.value.scene.primitives.add(e)}catch(e){console.warn("Failed to create 3D Tileset:",e)}a.value.scene.globe.depthTestAgainstTerrain=!0,v.value=setTimeout(()=>{a.value.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(105.8170876001954,32.**************,1e3),orientation:{heading:Cesium.Math.toRadians(340.69649430383816),pitch:Cesium.Math.toRadians(-28.671005249524338),roll:99180339393996e-19},duration:5});let e=document.createElement("div");e.id=1,e.style.position="absolute",e.style.width="240px",e.style.height="240px",e.style.textAlign="center",e.style.fontSize="30px",e.style.color="white",e.style.fontWeight="bold",e.style.lineHeight="50px",e.style.cursor="pointer";let t=`
		<div style="background:linear-gradient(to top, #f34e01, #e6403d);border-radius:10px;">上西则南社区</div>
    <img src="${F}" alt="" style="margin-top:10px" id='img'>
    `;e.innerHTML=t,a.value.cesiumWidget.container.appendChild(e);let o=Cesium.Cartesian3.fromDegrees(105.813663,32.447343,500);g.value=()=>{if(!(!a.value||!a.value.scene||!a.value.scene.canvas||!e))try{const i=a.value.scene.canvas.height,s=Cesium.SceneTransforms.wgs84ToWindowCoordinates?Cesium.SceneTransforms.wgs84ToWindowCoordinates(a.value.scene,o):new Cesium.Cartesian2(i/2,i/2);if(s&&e.parentNode){e.style.bottom=i-s.y+"px";const n=e.offsetWidth;e.style.left=s.x-n/2+"px"}}catch(i){console.warn("Cesium coordinate transformation failed:",i),e&&e.parentNode&&(e.style.bottom="50%",e.style.left="50%",e.style.transform="translateX(-50%)")}},a.value.scene.postRender.addEventListener(g.value),document.getElementById("img").animate([{transform:"translateY(20px)"}],{duration:3e3,easing:"linear",iterations:"Infinity",delay:0}),e.addEventListener("click",k);let u=new Cesium.ScreenSpaceEventHandler(a.value.scene.canvas);u.setInputAction(function(i){D();let s=a.value.camera.getPickRay(i.position),n=a.value.scene.globe.pick(s,a.value.scene),l=Cesium.Cartographic.fromCartesian(n),r=Cesium.Math.toDegrees(l.longitude),d=Cesium.Math.toDegrees(l.latitude),m=l.height,p={longitude:Number(r.toFixed(6)),latitude:Number(d.toFixed(6)),altitude:Number(m.toFixed(2))};console.log(p)},Cesium.ScreenSpaceEventType.LEFT_CLICK),[[105.818,32.446155,4e3],[105.827,32.456,4e3],[105.83,32.456,4e3],[105.8234,32.449,4e3]].forEach(i=>{var s=b({color:new Cesium.Color(.9411764705882353,.29411764705882354,.054901960784313725,.5),show:!0,positions:[105.809825,32.446426,10,105.817052,32.452105,10,105.81868,32.450572,10,105.813891,32.446415,10,105.811862,32.442634,10,105.811501,32.442315,10,105.809825,32.446426,10],wallHeight:500,hasHeight:!0});s.tag="areaDsLines",window.shiningWalls||(window.shiningWalls=[]),window.shiningWalls.push(s)}),u.value=new Cesium.ScreenSpaceEventHandler(a.value.scene.canvas),u.value.setInputAction(function(i){var s=a.value.scene.globe.ellipsoid,n=a.value.camera.pickEllipsoid(i.endPosition,s);if(n){var l=a.value.scene.globe.ellipsoid.cartesianToCartographic(n);Cesium.Math.toDegrees(l.latitude).toFixed(4),Cesium.Math.toDegrees(l.longitude).toFixed(4);var r=(a.value.camera.positionCartographic.height/1e3).toFixed(2);r>=1.5?e.style.display="none":e.style.display="block"}},Cesium.ScreenSpaceEventType.MOUSE_MOVE)},3e3)});function b(e){var t=e.color?e.color:Cesium.Color.RED,o=e.maxHeight?e.maxHeight:10,u=e.minHeight?e.minHeight:1,c=[],i=[],s=document.createElement("canvas");s.width=50,s.height=50;var n=s.getContext("2d"),l=n.createLinearGradient(0,20,0,0);l.addColorStop(0,"rgba("+t.red*255+","+t.green*255+","+t.blue*255+",1)"),l.addColorStop(1,"rgba("+t.red*255+","+t.green*255+","+t.blue*255+",0)"),n.fillStyle=l,n.fillRect(0,0,50,50);var r=null;if(e.hasHeight){var d=[];e.positions.forEach((C,w)=>{w%3==2?(i.push(C),c.push(C+(isNaN(e.wallHeight)?1:e.wallHeight))):d.push(C)}),r={wall:{show:e.show!=!1,positions:Cesium.Cartesian3.fromDegreesArray(d),material:new Cesium.ImageMaterialProperty({image:s,transparent:!0}),zIndex:1e3}}}else{for(var m=0;m<e.positions.length/2;m++)i.push(u),c.push(o);r={wall:{show:e.show!=!1,positions:Cesium.Cartesian3.fromDegreesArray(e.positions),material:new Cesium.ImageMaterialProperty({image:s,transparent:!0}),zIndex:1e3}}}r.wall.maximumHeights=c,r.wall.minimumHeights=i;var p=a.value.entities.add(r);return p.wall.material.color=new Cesium.CallbackProperty(function(C,w){var H=.5*Math.abs(Math.sin(new Date().getTime()/500))+.1;return t.withAlpha(H)},!1),p}function k(){f.push({path:"/grid"})}function D(){var e={},t=a.value.scene,o=t.globe.ellipsoid,u=t.canvas,c=a.value.camera.pickEllipsoid(new Cesium.Cartesian2(0,0),o),i=a.value.camera.pickEllipsoid(new Cesium.Cartesian2(u.width,u.height),o);if(c&&i){var s=o.cartesianToCartographic(c),n=o.cartesianToCartographic(i);e.xmin=Cesium.Math.toDegrees(s.longitude),e.ymax=Cesium.Math.toDegrees(s.latitude),e.xmax=Cesium.Math.toDegrees(n.longitude),e.ymin=Cesium.Math.toDegrees(n.latitude)}else if(!c&&i){var l=null,r=0;do r<=u.height?r+=10:u.height,l=a.value.camera.pickEllipsoid(new Cesium.Cartesian2(0,r),o);while(!l);var d=o.cartesianToCartographic(l),m=o.cartesianToCartographic(i);e.xmax=Cesium.Math.toDegrees(d.longitude),e.ymax=Cesium.Math.toDegrees(d.latitude),e.xmin=Cesium.Math.toDegrees(m.longitude),e.ymin=Cesium.Math.toDegrees(m.latitude)}return e.height=Math.ceil(a.value.camera.positionCartographic.height),e.lon=Cesium.Math.toDegrees(a.value.camera.positionCartographic.longitude),e.lat=Cesium.Math.toDegrees(a.value.camera.positionCartographic.latitude),e.heading=Cesium.Math.toDegrees(a.value.camera.heading),e.pitch=Cesium.Math.toDegrees(a.value.camera.pitch),e.roll=Cesium.Math.toDegrees(a.value.camera.roll),console.log("{'lon':"+e.lon+",'lat':"+e.lat+",'height':"+e.height+",'heading':"+e.heading+",'pitch':"+e.pitch+",'roll':"+e.roll+"}"),e}function E(){try{window&&window.history?window.history.back():parent&&parent.history?parent.history.back():f.back()}catch(e){console.error("导航返回失败:",e),f.back()}}return L(()=>{a.value&&a.value.scene&&g.value&&(a.value.scene.postRender.removeEventListener(g.value),g.value=null),a.value&&(a.value.destroy(),a.value=null),v.value&&(clearTimeout(v.value),v.value=null)}),(e,t)=>(x(),M(B,null,[N(y)?(x(),M("div",{key:0,class:"goback",onClick:E,style:{"font-size":"12px"}},t[0]||(t[0]=[T("span",{class:"backText"}," 返回",-1)]))):P("",!0),t[1]||(t[1]=T("div",{id:"cesiumContainer"},null,-1))],64))}},O=I(z,[["__scopeId","data-v-f6a33179"]]);export{O as default};
