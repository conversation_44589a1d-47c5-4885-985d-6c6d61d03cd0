import{_ as O,r as i,o as T,b as r,c as v,d as e,t as M,F as E,s as X,u as Z,j as Q,f as ee,E as ae,g as te,w as D,e as A,p as b,h as l,i as c,x as $}from"./index-e6e213aa.js";import{j as J,L as se,i as F,k as oe,s as ne,l as ie,t as le,q as re,r as de,m as ue,n as ce,o as ge,p as fe}from"./di02-e3875920.js";import{b as he,a as pe,c as me,t as ve}from"./bussinessCase-cf5b2069.js";import{a as be,p as ye,s as Se,b as _e,c as Ce,h as ke,r as we}from"./ageConstruction-5c8b4794.js";const $e={class:"_box"},Ge={class:"top"},De={class:"top"},Ae={class:"top"},He={class:"_box2"},xe={__name:"gridContent",props:{householder:Number,user_sum:Number,dataHuJi:Array,dataType:Array,dataSex:Array,dataFace:Array,xiaoqu:Number},emits:["increase"],setup(R,{emit:a}){const S=a,g=R,C=i(null),H=i(null),x=i(null),L=i(null);T(()=>{setTimeout(()=>{o(),z(),B(),q()},1e3)});function o(){const m=J(C.value),s=[[{offset:0,color:"rgba(15, 105, 240)"},{offset:1,color:"rgba(15, 105, 240, 1)"}],[{offset:0,color:"rgba(54, 234, 255, 1)"},{offset:1,color:"rgba(10, 165, 214, 1)"}]],f=["rgba(50, 255, 238, 1)","rgba(0, 233, 179, 1)","rgba(29, 246, 66, 1)","rgba(240, 255, 71, 1)","rgba(255, 213, 47, 1)","rgba(255, 126, 76, 1)","rgba(255, 96, 86, 1)","rgba(97, 187, 255, 1)"],y=g.dataHuJi.map((d,_)=>({name:d.name,value:d.num,itemStyle:{color:{type:"linear",colorStops:s[_]}}}));var u={title:{text:"户籍情况",left:"center",textStyle:{color:"#fff"},top:-4},series:[{type:"pie",radius:["30%","60%"],center:["50%","50%"],selectedMode:!1,hoverAnimation:!1,top:5,data:[{value:1,name:""}],itemStyle:{color:"#0e2c5f"},label:{show:!1},labelLine:{show:!1},tooltip:{show:!1}},{name:"户籍情况",type:"pie",radius:["30%","50%"],center:["50%","50%"],avoidLabelOverlap:!1,labelLine:{length:30,length2:100},top:5,label:{formatter:function(d){return"{a|"+d.data.name+` }

 {b|`+d.data.value+"}"},padding:[0,-110,-10,-110],rich:{a:{fontSize:14,color:"#fff",fontWeight:500,align:"center"},b:{fontSize:16,color:"#fff",fontWeight:"bold",align:"center"}}},data:y}],color:f,backgroundColor:"transparent"};u&&m.setOption(u)}function z(){const m=J(H.value),s=[[{offset:0,color:"rgba(15, 105, 240, 1)"},{offset:1,color:"rgba(15, 105, 240, 1)"}],[{offset:0,color:"rgba(54, 234, 255, 1)"},{offset:1,color:"rgba(10, 165, 214, 1)"}]],f=["rgba(50, 255, 238, 1)","rgba(0, 233, 179, 1)","rgba(29, 246, 66, 1)","rgba(240, 255, 71, 1)","rgba(255, 213, 47, 1)","rgba(255, 126, 76, 1)","rgba(255, 96, 86, 1)","rgba(97, 187, 255, 1)"],y=g.dataType.map((d,_)=>({name:d.name,value:d.num,itemStyle:{color:{type:"linear",colorStops:s[_]}}}));var u={title:{text:"人员类型",left:"center",textStyle:{color:"#fff"},top:-4},series:[{type:"pie",radius:["30%","60%"],center:["50%","50%"],selectedMode:!1,hoverAnimation:!1,top:5,data:[{value:1,name:""}],itemStyle:{color:"#0e2c5f"},label:{show:!1},labelLine:{show:!1},tooltip:{show:!1}},{name:"人员类型",type:"pie",top:5,radius:["30%","50%"],center:["50%","50%"],avoidLabelOverlap:!1,labelLine:{length:30,length2:80},label:{formatter:function(d){return console.log(d),"{a|"+d.data.name+` }

 {b|`+d.data.value+"}"},padding:[0,-80,-10,-80],rich:{a:{fontSize:14,color:"#fff",fontWeight:500,align:"center"},b:{fontSize:16,color:"#fff",fontWeight:"bold",align:"center"}}},data:y}],color:f,backgroundColor:"transparent"};u&&m.setOption(u)}function B(){const m=J(x.value),s=i(0);s.value=g.dataFace.reduce((h,u)=>h+=u.value,0);function f(h,u){let d=u||"value",_=[];return h&&h.forEach(function(G){_.push(G[d])}),_}var y={title:{text:"政治面貌",textStyle:{color:"#fff"},left:110},backgroundColor:"transparent",grid:{top:"20%",bottom:0,left:30,containLabel:!0},xAxis:{show:!1},yAxis:[{triggerEvent:!0,show:!0,inverse:!0,data:f(g.dataFace,"name"),axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1,interval:0,color:"red",align:"left",margin:80,fontSize:13,formatter:function(h,u){return"{title|"+h+"}"},rich:{title:{width:165}}}},{triggerEvent:!0,show:!0,inverse:!0,data:f(g.dataFace,"name"),axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{interval:0,shadowOffsetX:"-20px",color:"#0f72fe",fontWeight:"bold",align:"right",verticalAlign:"bottom",lineHeight:30,fontSize:14,fontStyle:"italic",formatter:function(h,u){return(g.dataFace[u].value/s.value*100).toFixed(1)+"%"}}}],series:[{name:"条",type:"bar",showBackground:!0,backgroundStyle:{borderRadius:30},barBorderRadius:30,yAxisIndex:0,data:g.dataFace,barWidth:9,itemStyle:{color:new se(0,0,1,0,[{offset:0,color:"#0f71fb "},{offset:1,color:"#81eff3"}],!1),barBorderRadius:10,barBorderRadius:4},label:{show:!0,color:"#fff",position:[0,"-20px"],textStyle:{fontSize:14},formatter:function(h,u){return` ${h.name}  {hou|${h.value}} 人`},rich:{hou:{fontWeight:"bold",fontSize:16}}}}]};y&&m.setOption(y)}function q(){const m=J(L.value);let s=g.dataSex.reduce((p,w)=>p+w.num,0);const f=[[{offset:0,color:"rgba(15, 114, 254, 1)"},{offset:1,color:"rgba(15, 114, 254, 1)"}],[{offset:0,color:"rgba(54, 234, 255, 1)"},{offset:1,color:"rgba(10, 165, 214, 1)"}]],y=["rgba(15, 114, 254, .3)","rgba(0, 56, 75, 1)"],h=["rgba(50, 255, 238, 1)","rgba(0, 233, 179, 1)","rgba(29, 246, 66, 1)","rgba(240, 255, 71, 1)","rgba(255, 213, 47, 1)","rgba(255, 126, 76, 1)","rgba(255, 96, 86, 1)","rgba(97, 187, 255, 1)"],u=g.dataSex.map((p,w)=>({name:p.name,value:p.num,itemStyle:{color:{type:"linear",colorStops:f[w]}}})),d=g.dataSex.map((p,w)=>({name:p.name,value:p.num,itemStyle:{color:y[w]}}));var G={title:{text:"性别构成",left:"center",textStyle:{color:"#fff"}},series:[{name:"性别比例",type:"pie",center:["50%","50%"],radius:[0,"50%"],top:20,label:{fontSize:14,position:"inner",formatter:p=>`${p.name}

${p.value}`,color:"white"},selectedMode:"single",data:d},{name:"性别比例",type:"pie",top:20,radius:["50%","80%"],center:["50%","50%"],avoidLabelOverlap:!1,label:{show:!0,position:"inside",fontSize:16,formatter:p=>`${(p.data.value/s*100).toFixed(0)}%`,color:"white",fontWeight:"bold"},data:u}],color:h,backgroundColor:"transparent"};G&&m.setOption(G)}function k(m){switch(m){case 6:S("dialogRenYuan");break;case 5:S("dialogHuJi");break;case 4:S("dialog2");break;case 3:S("dialogHu");break;case 2:S("dialogCommunity");break;case 1:S("dialogBranch");break}}return(m,s)=>(r(),v(E,null,[e("div",$e,[e("div",{class:"single",onClick:s[0]||(s[0]=f=>k(1))},s[6]||(s[6]=[e("div",{class:"top"},M(1),-1),e("img",{src:F,alt:""},null,-1),e("span",{class:"bottom"},"党支部（个）",-1)])),e("div",{class:"single",onClick:s[1]||(s[1]=f=>k(2))},[e("div",Ge,M(g.xiaoqu),1),s[7]||(s[7]=e("img",{src:F,alt:""},null,-1)),s[8]||(s[8]=e("span",{class:"bottom"},"小区（个）",-1))]),e("div",{class:"single",onClick:s[2]||(s[2]=f=>k(3))},[e("div",De,M(g.householder),1),s[9]||(s[9]=e("img",{src:F,alt:""},null,-1)),s[10]||(s[10]=e("span",{class:"bottom"},"户数（户）",-1))]),e("div",{class:"single",onClick:s[3]||(s[3]=f=>k(4))},[e("div",Ae,M(g.user_sum),1),s[11]||(s[11]=e("img",{src:F,alt:""},null,-1)),s[12]||(s[12]=e("span",{class:"bottom"},"人数（人）",-1))])]),e("div",He,[e("div",{ref_key:"huji",ref:C,id:"huji",onClick:s[4]||(s[4]=f=>k(5))},null,512),e("div",{ref_key:"renyuan",ref:H,id:"renyuan",onClick:s[5]||(s[5]=f=>k(6))},null,512),e("div",{ref_key:"zhengzhi",ref:x,id:"zhengzhi"},null,512),e("div",{ref_key:"sex",ref:L,id:"sex"},null,512)])],64))}},Me=O(xe,[["__scopeId","data-v-22158d80"]]);const Le={src:ce,alt:""},ze={src:ge,alt:""},Be={class:"biao"},qe=["src"],Je={key:0,class:"left"},Fe={class:"one"},Re={class:"one_dis"},Oe={class:"two"},Te={class:"two_dis"},Ee={key:1,class:"right"},Ie={class:"one"},je={class:"one_dis"},Ye={class:"two"},We={class:"two_dis"},Ne={key:2,class:"bottom"},Ue={class:"bot_dis"},Ve={class:"mengban"},Pe={class:"dialog"},Ke={key:3,class:"dialog"},Xe={key:4,class:"dialog"},Ze={class:"dialog"},Qe={class:"dialog"},ea={key:5,class:"dialog"},aa={key:6,class:"dialog"},ta={key:7,class:"dialog"},sa={key:8,class:"dialog"},oa={key:9,class:"dialog"},na={__name:"a",setup(R){const a=X(),S=Z(),g=Q(),C=i(""),H=i(!0),x=i(!0);i(!1);const L=i(1);i(!1),i(!1),i(!1),i(!1),i(!1),i(!1),i(!1),i(!1),i(!1),i(!1),i(!1),i(!1),i(!1);let o=ee({xiaoqu:0,subObj:{},householder:0,user_sum:0,dataHuJi:[],dataType:[],dataSex:[],dataFace:[],bussiness1:2,bussiness2:1,dataAge:[{name:"0-3",value:50},{name:"3-6",value:100},{name:"6-12",value:200},{name:"12-18",value:400},{name:"18-59",value:1e3},{name:"59-79",value:500},{name:"80以上",value:200}],dataStudent:[{name:"大学",value:200},{name:"高中",value:150},{name:"初中",value:200},{name:"小学",value:100}],dataSolier:[{name:"优抚对象",value:20},{name:"预备役",value:12},{name:"退役",value:10},{name:"现役",value:5}],dataPerson:[{value:30,name:"致富带头人"},{value:28,name:"文化名流"},{value:26,name:"职业能人"},{value:24,name:"其他"}],dataSpecial:[{value:3,name:"低保户"},{value:5,name:"失独家庭"},{value:9,name:"困境儿童"},{value:17,name:"孤寡老人"},{value:5,name:"残疾人"},{value:7,name:"其他"}],dataImportant:[{value:5,name:"社区矫正"},{value:5,name:"刑满释放"},{value:25,name:"精神病患者"},{value:1,name:"吸毒人员"},{value:5,name:"其他"}],dataInteraction:{data1:[10,5,8,2,20,5,10,6],data2:[20,10,5,3,10,5,20,6],data3:[4,20,6,10,5,20,6,10],data4:[5,10,6,20,2,10,5,6]},focusAge:1,focusStudent:1,focusSolier:1});const z=i(0);T(()=>{window.addEventListener("message",q),z.value=g.query.code,z.value==20?(C.value="/mapB",B("20")):(C.value="/mapA",B("10")),oe({limit:15,offset:0,type:"2",code:g.query.code,householder_name:""}).then(t=>{o.householder=t.data.count})});async function B(t){const n=await ne({type:"2",code:t});n.code==200?(o.user_sum=n.data.user_sum,o.dataHuJi=n.data.domicile_from,o.dataType=n.data.domicile_type,o.dataSex=n.data.gender,o.dataFace=n.data.political,o.dataAge=n.data.age,o.dataPerson=n.data.famous_type,o.dataSpecial=n.data.special_group,o.dataImportant=n.data.focus_type,o.dataSolier=n.data.soldier_type,o.dataStudent=n.data.student,o.xiaoqu=n.data.xiaoqu,o.subObj={teenager:n.data.teenager,teenager_percent:n.data.teenager_percent,middle_aged:n.data.middle_aged,middle_aged_percent:n.data.middle_aged_percent,old_aged:n.data.old_aged,old_aged_percent:n.data.old_aged_percent}):ae.error(n.message)}function q(t){let n=t.data.type;n=="a"&&H.value&&(H.value=!1,S.push("/specificCommunity?code="+t.data.value)),n=="b"&&x.value&&(x.value=!1,S.push("/specificCommunity?code="+t.data.value))}function k(){a.dispatch("changeZhan")}function m(t){L.value=t,a.dispatch("changeGridNo",t),a.dispatch("changeGridMask",!0),a.dispatch("changeGridClosed1",!0)}function s(){a.dispatch("changeGridMask",!1),a.dispatch("changeGridClosed1",!1)}function f(t){o.focusAge=t,a.dispatch("changeGridMask",!0),a.dispatch("changeGridClosedAge",!0),a.dispatch("changeGridUpdateData",!0)}function y(){a.dispatch("changeGridMask",!1),a.dispatch("changeGridClosedAge",!1),a.dispatch("changeGridUpdateData",!1)}function h(){a.dispatch("changeGridMask",!0),a.dispatch("changeGridClosedHu",!0)}function u(){a.dispatch("changeGridMask",!1),a.dispatch("changeGridClosedHu",!1)}function d(){a.dispatch("changeGridMask",!0),a.dispatch("changeGridClosedCommunity",!0)}function _(){a.dispatch("changeGridMask",!1),a.dispatch("changeGridClosedCommunity",!1)}function G(){a.dispatch("changeGridMask",!0),a.dispatch("changeGridClosedBranch",!0)}function p(){a.dispatch("changeGridMask",!1),a.dispatch("changeGridClosedBranch",!1)}function w(){a.dispatch("changeGridMask",!0),a.dispatch("changeGridClosed2",!0)}function I(){a.dispatch("changeGridMask",!1),a.dispatch("changeGridClosed2",!1)}function j(){a.dispatch("changeGridMask",!0),a.dispatch("changeGridClosedHuJi",!0)}function Y(){a.dispatch("changeGridMask",!1),a.dispatch("changeGridClosedHuJi",!1)}function W(){a.dispatch("changeGridMask",!0),a.dispatch("changeGridClosedRenYuan",!0)}function N(){a.dispatch("changeGridMask",!1),a.dispatch("changeGridClosedRenYuan",!1)}function U(t){o.focusStudent=t,a.dispatch("changeGridMask",!0),a.dispatch("changeGridClosedStudent",!0),a.dispatch("changeGridUpdateStudent",!0)}function V(){a.dispatch("changeGridMask",!1),a.dispatch("changeGridClosedStudent",!1),a.dispatch("changeGridUpdateStudent",!1)}function P(t){o.focusSolier=t,a.dispatch("changeGridMask",!0),a.dispatch("changeGridClosedSolier",!0),a.dispatch("changeGridUpdateSolier",!0)}function K(){a.dispatch("changeGridMask",!1),a.dispatch("changeGridClosedSolier",!1),a.dispatch("changeGridUpdateSolier",!1)}return te(()=>{C.value=C.value+"?"+new Date().getTime(),window.removeEventListener("message",q)}),(t,n)=>(r(),v(E,null,[e("div",{class:"goback",onClick:n[0]||(n[0]=ia=>t.$router.replace("/grid")),style:{"font-size":"12px"}},n[1]||(n[1]=[e("span",{class:"backText"}," 返回",-1)])),e("div",{class:"qiu",onClick:k},[D(e("img",Le,null,512),[[A,t.$store.state.show.zhan]]),D(e("img",ze,null,512),[[A,!t.$store.state.show.zhan]])]),e("div",Be,M(t.$route.query.code==10?"望凤街网格":"中兴街网格"),1),e("iframe",{src:C.value,frameborder:"0",class:"mapa"},null,8,qe),t.$store.state.show.zhan?c("",!0):(r(),v("div",Je,[e("div",Fe,[n[2]||(n[2]=e("div",{class:"title"},[e("span",null,"网格概况")],-1)),e("div",Re,[b(Me,{householder:l(o).householder,user_sum:l(o).user_sum,gridList:l(o).list,dataHuJi:l(o).dataHuJi,dataSex:l(o).dataSex,dataFace:l(o).dataFace,dataType:l(o).dataType,xiaoqu:l(o).xiaoqu,onDialogHu:h,onDialogCommunity:d,onDialogBranch:G,onDialog2:w,onDialogHuJi:j,onDialogRenYuan:W},null,8,["householder","user_sum","gridList","dataHuJi","dataSex","dataFace","dataType","xiaoqu"])])]),e("div",Oe,[n[3]||(n[3]=e("div",{class:"title"},[e("span",null,"经济结构")],-1)),e("div",Te,[b(he,{onDialogBussiness:m})])])])),t.$store.state.show.zhan?c("",!0):(r(),v("div",Ee,[e("div",Ie,[n[4]||(n[4]=e("div",{class:"title"},[e("span",null,"年龄结构")],-1)),e("div",je,[b(be,{dataAge:l(o).dataAge,subObj:l(o).subObj,onDialogAge:f},null,8,["dataAge","subObj"])])]),e("div",Ye,[n[5]||(n[5]=e("div",{class:"title"},[e("span",null,"人员类别")],-1)),e("div",We,[b(ye,{dataStudent:l(o).dataStudent,dataSolier:l(o).dataSolier,dataPerson:l(o).dataPerson,dataImportant:l(o).dataImportant,dataSpecial:l(o).dataSpecial,onDialog:m,onDialogStudent:U,onDialogSolier:P},null,8,["dataStudent","dataSolier","dataPerson","dataImportant","dataSpecial"])])])])),t.$store.state.show.zhan?c("",!0):(r(),v("div",Ne,[n[6]||(n[6]=e("div",{class:"title1"},[e("span",null,"民意互动情况统计")],-1)),e("div",Ue,[b(ie,{dataInteraction:l(o).dataInteraction},null,8,["dataInteraction"])])])),D(e("div",Ve,null,512),[[A,t.$store.state.grid.isClosed]]),D(e("div",Pe,[t.$store.state.grid.no===1?(r(),$(le,{key:0,onClose:s,type:"2",code:t.$route.query.code},null,8,["code"])):c("",!0),t.$store.state.grid.no===2?(r(),$(re,{key:1,onClose:s,type:"2",code:t.$route.query.code},null,8,["code"])):c("",!0),t.$store.state.grid.no===3?(r(),$(de,{key:2,onClose:s,type:"2",code:t.$route.query.code},null,8,["code"])):c("",!0),t.$store.state.grid.no===4?(r(),$(ve,{key:3,onClose:s,type:"1",code:""})):c("",!0)],512),[[A,t.$store.state.grid.isClosed1]]),t.$store.state.grid.isClosedAge?(r(),v("div",Ke,[t.$store.state.grid.updateData?(r(),$(ue,{key:0,onCloseAge:y,focusAge:l(o).focusAge||t.$store.state.grid.age},null,8,["focusAge"])):c("",!0)])):c("",!0),t.$store.state.grid.isClosedHu?(r(),v("div",Xe,[b(fe,{onCloseHu:u})])):c("",!0),D(e("div",Ze,[b(pe,{onCloseCommunity:_})],512),[[A,t.$store.state.grid.isClosedCommunity]]),D(e("div",Qe,[b(me,{onCloseBranch:p})],512),[[A,t.$store.state.grid.isClosedBranch]]),t.$store.state.grid.isClosed2?(r(),v("div",ea,[b(Ce,{onClose2:I})])):c("",!0),t.$store.state.grid.isClosedHuJi?(r(),v("div",aa,[b(ke,{onCloseHuJi:Y})])):c("",!0),t.$store.state.grid.isClosedRenYuan?(r(),v("div",ta,[b(we,{onCloseRenYuan:N})])):c("",!0),t.$store.state.grid.isClosedStudent?(r(),v("div",sa,[t.$store.state.grid.updateStudent?(r(),$(Se,{key:0,onCloseStudent:V,focusStudent:l(o).focusStudent||t.$store.state.grid.student},null,8,["focusStudent"])):c("",!0)])):c("",!0),t.$store.state.grid.isClosedSolier?(r(),v("div",oa,[t.$store.state.grid.updateSolier?(r(),$(_e,{key:0,onCloseSolier:K,focusSolier:l(o).focusSolier||t.$store.state.grid.solier},null,8,["focusSolier"])):c("",!0)])):c("",!0)],64))}},ca=O(na,[["__scopeId","data-v-b7834dbf"]]);export{ca as default};
