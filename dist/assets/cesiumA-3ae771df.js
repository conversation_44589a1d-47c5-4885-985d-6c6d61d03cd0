import{_ as y,s as _,r as C,f as T,o as b,g as E,b as H,c as S}from"./index-e6e213aa.js";const k={id:"cesiumContainer"},D={__name:"cesiumA",setup(L){_();let m=C(null),e=C(null),p=T({data:[{lng:105.81203,lat:32.444008,infoId:2026,text:"阳光水岸小区"},{lng:105.812476,lat:32.444794,infoId:2025,text:"刑警队住宿楼"},{lng:105.811654,lat:32.445405,infoId:2013,text:"工行小区"},{lng:105.812814,lat:32.446311,infoId:2027,text:"樱花小区"},{lng:105.810693,lat:32.445391,infoId:2021,text:"蔷薇小区"},{lng:105.810715,lat:32.445816,infoId:2016,text:"皇佳公寓"},{lng:105.810417,lat:32.446181,infoId:2015,text:"和顺嘉苑"},{lng:105.811796,lat:32.447072,infoId:2029,text:"则天二小区"},{lng:105.812215,lat:32.446954,infoId:2030,text:"则天一小区"},{lng:105.811091,lat:32.446584,infoId:2024,text:"信用社住宿楼"},{lng:105.812182,lat:32.446527,infoId:2017,text:"金矿新区"},{lng:105.811292,lat:32.445192,infoId:2022,text:"嘉利水岸花园"},{lng:105.812737,lat:32.447696,infoId:2023,text:"兴和万科花园"},{lng:105.813013,lat:32.448363,infoId:2018,text:"金龙小区"},{lng:105.813306,lat:32.44815,infoId:2019,text:"林业站小区"},{lng:105.81304,lat:32.447036,infoId:2028,text:"雍江皇庭小区"},{lng:105.813357,lat:32.447368,infoId:2032,text:"自来水公司住宿楼"},{lng:105.813983,lat:32.447098,infoId:2031,text:"装备处住宿楼"},{lng:105.814637,lat:32.447582,infoId:2012,text:"工商所住宿楼"},{lng:105.813661,lat:32.446842,infoId:2020,text:"派出所住宿楼"},{lng:105.81339,lat:32.446651,infoId:2014,text:"管委会住宿楼"},{lng:105.81213,lat:32.447855,infoId:2010,text:"陈怀宽住宿楼"},{lng:105.812479,lat:32.448122,infoId:2011,text:"二建公司住宿楼"}],list:[{value1:2,value2:"网格数(个)"},{value1:34,value2:"小区(个)"},{value1:2468,value2:"户数(户)"},{value1:5623,value2:"人数(人)"}]});b(()=>{Cesium.Ion.defaultAccessToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.oeQSme1YSkYiiXbr26yD0qSVT1yTIAwNLjDOkcCGg7M",e.value=new Cesium.Viewer("cesiumContainer",{geocoder:!1,sceneModePicker:!1,navigationHelpButton:!1,baseLayerPicker:!0,homeButton:!1,fullscreenButton:!1,timeline:!1,animation:!1,shouldAnimate:!0,infoBox:!1,selectionIndicator:!1,baseLayerPicker:!1,terrainProvider:new Cesium.EllipsoidTerrainProvider}),e.value.cesiumWidget.creditContainer.style.display="none",e.value.camera.setView({destination:Cesium.Cartesian3.fromDegrees(105.81466431496081,32.**************,600),orientation:{heading:Cesium.Math.toRadians(340.72605932003046),pitch:Cesium.Math.toRadians(-15.***************),roll:.006995150196427984}});try{const t=new Cesium.Cesium3DTileset({url:"https://cdn.zn.nextv.show/zn/tileset.json"});t.readyPromise.then(()=>{console.log("3D Tileset loaded successfully")}).catch(l=>{console.warn("3D Tileset failed to load:",l),e.value&&e.value.scene&&e.value.scene.primitives.remove(t)}),e.value.scene.primitives.add(t)}catch(t){console.warn("Failed to create 3D Tileset:",t)}e.value.scene.globe.depthTestAgainstTerrain=!0,e.value.scene.screenSpaceCameraController.maximumZoomDistance=2e3,p.data.map((t,l)=>{e.value.entities.add({id:`area${l}`,infoId:t.infoId,position:new Cesium.Cartesian3.fromDegrees(t.lng,t.lat,500),billboard:{image:"/zuobiao.png",scale:.5,pixelOffset:new Cesium.Cartesian2(0,20)},label:{text:t.text.split("").join(`
`),font:"500 30px Helvetica",scale:.5,style:Cesium.LabelStyle.FILL,fillColor:Cesium.Color.WHITE,showBackground:!0,backgroundColor:new Cesium.Color(228,76,76,1),horizontalOrigin:Cesium.HorizontalOrigin.CENTER,verticalOrigin:Cesium.VerticalOrigin.BASELINE}})}),I(),new Cesium.ScreenSpaceEventHandler(e.value.scene.canvas).setInputAction(function(t){let l=e.value.camera.getPickRay(t.position),o=e.value.scene.globe.pick(l,e.value.scene),n=Cesium.Cartographic.fromCartesian(o),s=Cesium.Math.toDegrees(n.longitude),u=Cesium.Math.toDegrees(n.latitude),c=n.height,r={longitude:Number(s.toFixed(6)),latitude:Number(u.toFixed(6)),altitude:Number(c.toFixed(2))};console.log(r)},Cesium.ScreenSpaceEventType.LEFT_CLICK),[[105.818,32.446155,4e3],[105.827,32.456,4e3],[105.83,32.456,4e3],[105.8234,32.449,4e3]].forEach(t=>{var l=x({color:new Cesium.Color(.16470588235294117,.6470588235294118,.9686274509803922,.5),show:!0,positions:[105.809825,32.446426,10,105.813169,32.44911,10,105.815061,32.447432,10,105.813891,32.446415,10,105.811862,32.442634,10,105.811501,32.442315,10,105.809825,32.446426,10],wallHeight:480,hasHeight:!0});l.tag="areaDsLines",window.shiningWalls||(window.shiningWalls=[]),window.shiningWalls.push(l)})});function I(){m.value=new Cesium.ScreenSpaceEventHandler(e.value.scene.canvas),m.value.setInputAction(function(a){let i=e.value.scene.pick(a.position);i&&i.id&&e.value.entities.values.map(t=>{i.id.id&&t.id==i.id.id&&t.infoId&&window.parent.postMessage({type:"a",value:t.infoId,name:t.label.text._value},"*")})},Cesium.ScreenSpaceEventType.LEFT_CLICK),e.value.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)}function x(a){var i=a.color?a.color:Cesium.Color.RED,t=a.maxHeight?a.maxHeight:10,l=a.minHeight?a.minHeight:1,o=[],n=[],s=document.createElement("canvas");s.width=50,s.height=50;var u=s.getContext("2d"),c=u.createLinearGradient(0,20,0,0);c.addColorStop(0,"rgba("+i.red*255+","+i.green*255+","+i.blue*255+",1)"),c.addColorStop(1,"rgba("+i.red*255+","+i.green*255+","+i.blue*255+",0)"),u.fillStyle=c,u.fillRect(0,0,50,50);var r=null;if(a.hasHeight){var g=[];a.positions.forEach((d,h)=>{h%3==2?(n.push(d),o.push(d+(isNaN(a.wallHeight)?1:a.wallHeight))):g.push(d)}),r={wall:{show:a.show!=!1,positions:Cesium.Cartesian3.fromDegreesArray(g),material:new Cesium.ImageMaterialProperty({image:s,transparent:!0}),zIndex:1e3}}}else{for(var f=0;f<a.positions.length/2;f++)n.push(l),o.push(t);r={wall:{show:a.show!=!1,positions:Cesium.Cartesian3.fromDegreesArray(a.positions),material:new Cesium.ImageMaterialProperty({image:s,transparent:!0}),zIndex:1e3}}}r.wall.maximumHeights=o,r.wall.minimumHeights=n;var v=e.value.entities.add(r);return v.wall.material.color=new Cesium.CallbackProperty(function(d,h){var w=.5*Math.abs(Math.sin(new Date().getTime()/500))+.1;return i.withAlpha(w)},!1),v}return E(()=>{e.value.destroy(),e.value=null}),(a,i)=>(H(),S("div",k))}},A=y(D,[["__scopeId","data-v-2e4c7577"]]);export{A as default};
