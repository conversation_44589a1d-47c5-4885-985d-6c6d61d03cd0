# 生产环境调试指南

## 🔍 问题诊断

生产环境黑屏问题的调试版本已准备就绪！

## ✅ 已修复的潜在问题

### 1. 路由配置优化
- 为Vue Router添加了明确的base路径配置
- 确保生产环境路由正常工作

### 2. 资源路径修复
- 将Vite base路径从 `'./'` 改为 `'/'`
- 使用绝对路径避免资源加载问题

### 3. 添加调试信息
- 全局错误捕获
- Promise错误捕获
- Vue应用挂载状态检查
- 详细的控制台日志

## 🚀 部署和测试步骤

### 1. 推送代码
```bash
git add .
git commit -m "修复生产环境黑屏问题，添加调试信息"
git push origin main
```

### 2. 部署后检查
访问 https://ioc.zn.nextv.show/ 并：

1. **打开浏览器开发者工具**
   - 按F12或右键选择"检查"
   - 切换到Console标签页

2. **查看控制台日志**
   应该看到以下日志：
   ```
   Environment check: {location: "https://...", userAgent: "...", timestamp: "..."}
   Main.js loaded, starting Vue app...
   Vue app created
   Router installed
   Store installed
   ElementPlus installed
   Vue app mounted successfully
   ```

3. **检查Network标签页**
   - 确认所有资源正常加载（状态码200）
   - 特别注意JS、CSS文件是否404

4. **查看页面错误提示**
   - 如果有错误，页面上会显示红色/橙色的错误信息框

## 🔧 常见问题排查

### 问题1：资源404错误
**症状**：Network标签显示JS/CSS文件404
**解决**：检查Nginx配置，确保静态文件路径正确

### 问题2：Vue应用未挂载
**症状**：5秒后显示"Vue app not mounted"
**可能原因**：
- JavaScript执行错误
- 依赖加载失败
- 路由配置问题

### 问题3：白屏但无错误
**症状**：页面空白，控制台无错误
**检查**：
- 确认Vue Router是否正确重定向到/login
- 检查登录组件是否正常加载

### 问题4：Cesium相关错误
**症状**：3D地图功能报错
**检查**：
- 确认/cesium/目录是否存在
- 检查Cesium.js是否正常加载

## 📊 调试信息说明

### 控制台日志含义
- `Environment check`: 环境信息检查
- `Main.js loaded`: 主文件加载成功
- `Vue app created`: Vue应用创建成功
- `Router installed`: 路由安装成功
- `Store installed`: 状态管理安装成功
- `ElementPlus installed`: UI组件库安装成功
- `Vue app mounted successfully`: 应用挂载成功

### 错误提示框
- **红色框**：JavaScript执行错误
- **橙色框**：Vue应用挂载超时

## 🎯 下一步操作

1. **部署调试版本**
2. **收集错误信息**
3. **根据错误信息进一步修复**
4. **移除调试代码**（问题解决后）

## 📞 如果问题仍然存在

请提供以下信息：
1. 浏览器控制台的完整日志
2. Network标签页的资源加载状态
3. 页面上显示的任何错误信息
4. 服务器Nginx错误日志

## 🔄 移除调试代码

问题解决后，可以移除调试代码：
1. 删除index.html中的调试脚本
2. 简化main.js中的日志输出
3. 重新构建和部署
